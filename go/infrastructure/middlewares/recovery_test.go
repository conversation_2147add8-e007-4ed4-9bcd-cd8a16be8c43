package middlewares

//
//import (
//	"bytes"
//	"contentmanager/logging"
//	utils "contentmanager/tests/test_utils"
//	"net/http"
//	"net/http/httptest"
//	"testing"
//)
//
//func Test_Recovery(t *testing.T) {
//	buff := bytes.NewBufferString("")
//	logging.LogOutput = buff
//
//	recorder := httptest.NewRecorder()
//	m := New()
//	// replace log for testing
//
//	m.Use(func(res http.ResponseWriter, req *http.Request) {
//		res.Header().Set("Content-Type", "unpredictable")
//	})
//	m.Use(Recovery())
//	m.Use(func(res http.ResponseWriter, req *http.Request) {
//		panic("here is a panic!")
//	})
//	m.ServeHTTP(recorder, &http.Request{})
//
//	utils.Expect(t, recorder.Code, http.StatusInternalServerError)
//	utils.Expect(t, recorder.Header().Get("Content-Type"), "unpredictable")
//	utils.Refute(t, recorder.Body.Len(), 0)
//	utils.Refute(t, len(buff.String()), 0)
//}
//
//func Test_Recovery_ResponseWriter(t *testing.T) {
//	recorder := httptest.NewRecorder()
//	recorder2 := httptest.NewRecorder()
//
//	m := New()
//	m.Use(Recovery())
//	m.Use(func(c Context) {
//		c.MapTo(recorder2, (*http.ResponseWriter)(nil))
//		panic("here is a panic!")
//	})
//	m.ServeHTTP(recorder, &http.Request{})
//
//	utils.Expect(t, recorder2.Code, http.StatusInternalServerError)
//	utils.Refute(t, recorder2.Header().Get("Content-Type"), "text/html")
//	utils.Refute(t, recorder2.Body.Len(), 0)
//}
