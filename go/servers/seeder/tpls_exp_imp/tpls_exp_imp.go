package tpls_exp_imp

import (
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/olm"
	"contentmanager/pkgs/service_context"
	"contentmanager/pkgs/structure"
	"contentmanager/servers/seeder/fs"
	"fmt"
	"path"
)

func Export() {
	folder := `D:\crawler\templates\prod\hbs`

	serviceCtx := service_context.NewServiceContext("tpls_exp_imp")

	for _, tenant := range serviceCtx.Tenants() {
		tenantDB := serviceCtx.TenantDB(tenant.ID)
		suffix := path.Join(folder, tenant.Server)
		fs.MustEnsureFolderExists(suffix)

		var contents []content.Content
		var structures []structure.Structure
		var lists []olm.List

		if err := tenantDB.Where("active and type in('template')").Find(&contents).Error; err != nil {
			serviceCtx.Logger().Error().Err(err).Msg("Failed to get contents")
		} else {
			suffixTemplates := path.Join(suffix, "templates")
			fs.MustEnsureFolderExists(suffixTemplates)

			for _, content := range contents {
				fileName := path.Join(suffixTemplates, fmt.Sprintf("%s--%s.hbs", fs.SanitizeFilename(content.Title), content.ID.String()))
				fs.MustWriteFile(fileName, content.Content)
				serviceCtx.Logger().Info().Str("file", fileName).Msg("Exported content")
			}
		}

		if err := tenantDB.Where("active").Find(&structures).Error; err != nil {
			serviceCtx.Logger().Error().Err(err).Msg("Failed to get structures")
		} else {
			suffixStructures := path.Join(suffix, "structures")
			fs.MustEnsureFolderExists(suffixStructures)

			for _, s := range structures {
				fileName := path.Join(suffixStructures, fmt.Sprintf("%s--%s.hbs", fs.SanitizeFilename(s.Name), s.ID.String()))
				fs.MustWriteFile(fileName, s.Template)
				serviceCtx.Logger().Info().Str("file", fileName).Msg("Exported structure")
			}
		}

		if err := tenantDB.Where("active").Find(&lists).Error; err != nil {
			serviceCtx.Logger().Error().Err(err).Msg("Failed to get lists")
		} else {
			suffixLists := path.Join(suffix, "lists")
			fs.MustEnsureFolderExists(suffixLists)

			for _, l := range lists {
				fileName := path.Join(suffixLists, fmt.Sprintf("%s--%s.hbs", fs.SanitizeFilename(l.Name), l.ID.String()))
				fs.MustWriteFile(fileName, l.Template)
				serviceCtx.Logger().Info().Str("file", fileName).Msg("Exported list")
			}
		}

	}
}
