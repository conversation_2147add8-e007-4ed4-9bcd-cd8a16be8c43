package perf

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/search_v2/index"
	"contentmanager/pkgs/service_context"
	"contentmanager/servers/crawler/client"
	uuid "github.com/satori/go.uuid"
	"io"
	"net/http"
	"strings"
	"time"
)

var now = time.Now()

func RunMediaLoad() {
	serviceCtx := service_context.NewServiceContext("RunOnceFromLocal")
	if err := serviceCtx.Context().Err(); err != nil {
		panic(err)
	}

	urls := map[string]struct{}{}

	for _, tenant := range serviceCtx.Tenants() {
		indexingContext, err := index.ContextFromServiceContextForTenant(serviceCtx, tenant)
		if err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to create indexing context")
			continue
		}

		var images []commonModels.Media
		if err := indexingContext.TenantDB.
			Where("type = ?", "image").
			Where("active").
			Limit(100).
			Find(&images).Error; err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to fetch pages")
			continue
		}

		sitesMap := slicexx.AsMap(indexingContext.Sites, func(s models.Site) uuid.UUID {
			return s.ID
		})
		for _, image := range images {
			if image.Filename == "" {
				continue
			}
			rnd := slicexx.Random(image.Sites)
			if site, ok := sitesMap[rnd]; ok {
				base := site.PrimaryDomain
				if !strings.HasSuffix(base, "localhost") {
					base = base + ".localhost"
				}

				urls["https://"+base+"/images/"+image.Filename] = struct{}{}
			}
		}
	}

	StartDownloads(urls, 20)
}

func RunLoadTest() {
	serviceCtx := service_context.NewServiceContext("RunOnceFromLocal")
	if err := serviceCtx.Context().Err(); err != nil {
		panic(err)
	}

	for _, tenant := range serviceCtx.Tenants() {
		indexingContext, err := index.ContextFromServiceContextForTenant(serviceCtx, tenant)
		if err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to create indexing context")
			continue
		}

		var pages []commonModels.Content
		if err := indexingContext.TenantDB.
			Where("type = ?", "page").
			Where("active").
			Where(pgxx.PublishPeriod(now, "", false)).
			Where("privacy_level = 0").
			Find(&pages).Error; err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to fetch pages")
			continue
		}

		var urls = map[string]struct{}{}
		sitesMap := slicexx.AsMap(indexingContext.Sites, func(s models.Site) uuid.UUID {
			return s.ID
		})
		for _, page := range pages {
			if page.Route == "" || !strings.HasPrefix(page.Route, "/") {
				continue
			}
			rnd := slicexx.Random(page.Sites)
			if site, ok := sitesMap[rnd]; ok {
				base := site.PrimaryDomain
				if !strings.HasSuffix(base, "localhost") {
					base = base + ".localhost"
				}

				urls["http://"+base+page.Route] = struct{}{}
			}

		}
		indexingContext.TenantDB = nil

		tenantName := tenant.Name

		go func() {
			for {
				for u, _ := range urls {
					var httpClient = client.NewHTTPClient(client.DefaultConfig())
					req, err := http.NewRequestWithContext(serviceCtx.Context(), "GET", u, nil)

					res, err := httpClient.Do(req)
					if err != nil {
						serviceCtx.Logger().Error().Err(err).Str("tenant", tenantName).Msg("Failed to fetch page")
					}
					if res == nil {
						continue
					}
					_, err = io.Copy(io.Discard, res.Body)
					if err != nil {
						serviceCtx.Logger().Error().Err(err).Str("tenant", tenantName).Msg("Failed to fetch page")
					}
				}
			}
		}()
	}

	serviceCtx.Logger().Info().Msg("Running requests. Waiting for Ctrl+C")

	<-serviceCtx.Context().Done()
}
