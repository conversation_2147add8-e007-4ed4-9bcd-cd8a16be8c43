package converters

import (
	uuid "github.com/satori/go.uuid"
	"strconv"
	"strings"
)

// ToBool converts strings "1", "T", "TRUE" to true
// "0", "F", "FALSE" to false
func ToBool(s string, def bool) bool {
	switch strings.ToUpper(s) {
	case "1", "T", "TRUE":
		return true
	case "0", "F", "FALSE":
		return false
	}
	return def
}

func ToInt(s string, def int) int {
	if i, e := strconv.Atoi(s); e == nil {
		return i
	}
	return def
}
func GetBooleanValueFromQueryString(queryString []string, defaultValue bool) bool {
	var booleanValue bool
	if len(queryString) > 0 {
		booleanValue, _ = strconv.ParseBool(queryString[0])
	} else {
		booleanValue = defaultValue
	}
	return booleanValue
}

func ToUUIDOrNil(s *string) *uuid.UUID {
	if s == nil {
		return nil
	}
	u, err := uuid.FromString(FromPointer(s))
	if err != nil {
		return nil
	}
	return &u
}

func MapArray[T any, S any](items []T, f func(T) S) []S {
	res := make([]S, len(items))
	for i, item := range items {
		res[i] = f(item)
	}
	return res
}

// AsPointer returns a pointer to the passed value.
func AsPointer[T any](t T) *T {
	return &t
}

// AsPointerOrNil returns a pointer to the passed value, or nil, if the passed value is a zero value.
// If the passed value has `IsZero() bool` method (for example, time.Time instance),
// it is used to determine if the value is zero.
func AsPointerOrNil[T comparable](t T) *T {
	if z, ok := any(t).(interface{ IsZero() bool }); ok {
		if z.IsZero() {
			return nil
		}
		return &t
	}

	var zero T
	if t == zero {
		return nil
	}
	return &t
}

// FromPointer returns the value from the passed pointer or the zero value if the pointer is nil.
func FromPointer[T any](t *T) T {
	if t == nil {
		var zero T
		return zero
	}
	return *t
}
