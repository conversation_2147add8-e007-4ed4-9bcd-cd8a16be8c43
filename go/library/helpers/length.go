package helpers

import (
	commonModels "contentmanager/library/tenant/common/models"
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	uuid "github.com/satori/go.uuid"
)

func init() {
	handlebars.RegisterHelper("uuidArrayLength", length[uuid.UUID])
	handlebars.RegisterHelper("stringArrayLength", length[string])
	handlebars.RegisterHelper("tagArrayLength", length[commonModels.Tag])
	handlebars.RegisterHelper("contentArrayLength", length[publicModels.ContentForHandlebars])
}

func length[T any](s []T) int {
	return len(s)
}
