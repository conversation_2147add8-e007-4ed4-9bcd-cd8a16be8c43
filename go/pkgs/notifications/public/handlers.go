package public

import (
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/templates/hbs_helpers"
	publicDataaccess "contentmanager/library/tenant/public/dataaccess"
	publicServices "contentmanager/library/tenant/public/services"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/utils"
	"contentmanager/logging"
	emailGenerators2 "contentmanager/pkgs/notifications/email_generators"
	"contentmanager/pkgs/notifications/mailer"
	subscriptions2 "contentmanager/pkgs/notifications/public/subscriptions"
	"contentmanager/pkgs/notifications/templating"
	"contentmanager/pkgs/notifications/templating/viewmodels"
	"contentmanager/pkgs/notifications/validating"
	_ "embed"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
)

//go:embed templates/subscribe.hbs
var subscribeTemplate string

//go:embed templates/message.hbs
var messageTemplate string

//go:embed templates/manage_link.hbs
var manageLinkTemplate string

//go:embed templates/confirm.hbs
var confirmTemplate string

var subscribeHbs = handlebars.MustParse(subscribeTemplate)
var messageHbs = handlebars.MustParse(messageTemplate)
var confirmHbs = handlebars.MustParse(confirmTemplate)
var manageLinkHbs = handlebars.MustParse(manageLinkTemplate)

func init() {
	subscribeHbs.RegisterPartialTemplate("message", messageHbs)
}

type SubscriptionsViewModel struct {
	ManageSubscriptions subscriptions2.ViewModel
	ErrorMessage        string
	SuccessMessage      string
}

type MessageViewModel struct {
	ErrorMessage   string
	SuccessMessage string
	ManageLink     string
}

func PreviewGet(w http.ResponseWriter, r *shared.AppContext) {
	confAccessor := emailGenerators2.NewSiteConfigAccessor(r.TenancyDB())
	svm, svmErr := confAccessor.GetSingle(r.CurrentSiteID())
	if svmErr != nil {
		showResult(w, r, svmErr, "")
		return
	}

	vm := viewmodels.HTMLMessageModel{
		SiteViewModel: svm,
		IssueViewModel: viewmodels.IssueViewModel{
			Subject:        "Preview template",
			PreheaderText:  "(Optional) This text will appear in the inbox preview, but not the email body. It can be used to supplement the email subject line or even summarize the email's contents. Extended text preheaders (~490 characters) seems like a better UX for anyone using a screenreader or voice-command apps like Siri to dictate the contents of an email. If this text is not included, email clients will automatically populate it using the text (including image alt text) at the start of the email's body.",
			PreheaderSpace: "&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;",
		},
		Body: "<h1>Header</h1>",
	}

	helpers := hbsHelpers.NewHbsHelpers(r)
	html, err := templating.Templates.Preview.ExecWithHelpers(vm, helpers)
	showResult(w, r, err, html)
}

func PreviewDistrictGet(w http.ResponseWriter, r *shared.AppContext) {
	confAccessor := emailGenerators2.NewDistrictConfigAccessor(r.TenancyDB())
	svm, svmErr := confAccessor.Get([]uuid.UUID{r.CurrentSiteID()})
	if svmErr != nil {
		showResult(w, r, svmErr, "")
		return
	}

	vm := viewmodels.HTMLMessageModel{
		SiteViewModel: svm,
		IssueViewModel: viewmodels.IssueViewModel{
			Subject:        "Preview template",
			PreheaderText:  "(Optional) This text will appear in the inbox preview, but not the email body. It can be used to supplement the email subject line or even summarize the email's contents. Extended text preheaders (~490 characters) seems like a better UX for anyone using a screenreader or voice-command apps like Siri to dictate the contents of an email. If this text is not included, email clients will automatically populate it using the text (including image alt text) at the start of the email's body.",
			PreheaderSpace: "&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;",
		},
		Body: "<h1>Header</h1>",
	}

	helpers := hbsHelpers.NewHbsHelpers(r)
	html, err := templating.Templates.Preview.ExecWithHelpers(vm, helpers)
	showResult(w, r, err, html)
}

// SubscribeGet handles GET requests to /subscribe
// Can be overridden by a page with route: /sys-notifications-subscribe
func SubscribeGet(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	if results, err := getOverride(r, "/sys-notifications-subscribe"); err == nil {
		showResult(w, r, nil, results)
		return
	} else if !utils.IsErrorGormRecordNotFound(err) {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("failed to compile content chain in /sys/notification/subscribe")
	}

	res := subscriptions2.GetSubscriptions(r)

	html, err := publicServices.CompileTemplate(r, SubscriptionsViewModel{
		ManageSubscriptions: res.Data,
	}, subscribeHbs)

	showResult(w, r, err, html)
}

func SubscribePost(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	subscriber := parseSubscriber(r.Request().Form)
	subscriptionsMap := parseSubscriptions(r.Request().Form)

	mailer, mErr := transactionalMailer(r)
	if mErr != nil {
		logging.FromContext(r.Request().Context()).Error().Err(mErr).Interface("Subscriber", subscriber).Msg("Can't initialize mailer")
		showErrorMessage(w, r, "We can't process your request now, something is wrong on our side - please try again later.")
		return
	}

	res := subscriptions2.SubscribeAndSendEmail(r, mailer, subscriber, subscriptionsMap)

	if res.IsError() {
		// Possible errors:
		// SMTP issue: `535 Authentication Credentials Invalid`
		logging.FromContext(r.Request().Context()).Error().Err(res.Unwrap()).Interface("Subscriber", subscriber).Msg("Unable to subscribe")
		showErrorMessage(w, r, "It seems there is an issue on our end. Please try again later, and we'll do our best to have everything up and running smoothly.")
		return
	}

	html, err := publicServices.CompileTemplate(r, map[string]string{
		"SuccessMessage": res.Data,
	}, messageHbs)

	showResult(w, r, err, html)
}

// ManageGet handles GET requests to /manage/{secret}
// Can be overridden by a page with route: /sys-notifications-manage
func ManageGet(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	secret, e := uuid.FromString(p["secret"])
	if e != nil {
		panic(e)
	}

	if results, err := getOverride(r, "/sys-notifications-manage"); err == nil {
		showResult(w, r, nil, results)
		return
	} else if !utils.IsErrorGormRecordNotFound(err) {
		logging.FromContext(r.Request().Context()).Error().
			Err(err).
			Str("secret", secret.String()).
			Msg("failed to compile content chain in /sys/notification/manage/:secret")
	}

	var html string
	var err error

	res := subscriptions2.GetSubscriptionsByManageCode(r, secret)
	if res.IsSuccess() {
		html, err = publicServices.CompileTemplate(r, SubscriptionsViewModel{
			ManageSubscriptions: res.Data,
		}, subscribeHbs)
	}

	showResult(w, r, err, html)
}

func ManagePost(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	secret, e := uuid.FromString(p["secret"])
	if e != nil {
		panic(e)
	}
	subscriber := parseSubscriber(r.Request().Form)
	subscriptionsMap := parseSubscriptions(r.Request().Form)

	var html string
	var err error

	mailer, mErr := transactionalMailer(r)
	if mErr != nil {
		showResult(w, r, err, html)
		return
	}

	res := subscriptions2.ManageExisting(r, mailer, secret, subscriber, subscriptionsMap)
	if res.IsSuccess() {
		html, err = publicServices.CompileTemplate(r, map[string]string{
			"SuccessMessage": res.Data,
		}, messageHbs)
	}

	showResult(w, r, err, html)
}

type ApiRequest struct {
	FirstName string
	LastName  string
	Email     string
	Topics    map[uuid.UUID]bool
}

func SubscribeAPI_GET(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	utils.WriteResultJSON(w, subscriptions2.GetSubscriptions(r))
}

func SubscribeAPI_POST(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	var req ApiRequest
	if err := binding.JSON.Bind(r.Request(), &req); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	subscriber := subscriptions2.Subscriber{
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Email:     req.Email,
	}
	subscriptionsMap := req.Topics

	mailer, mErr := transactionalMailer(r)
	if mErr != nil {
		logging.FromContext(r.Request().Context()).Error().Err(mErr).Interface("Subscriber", subscriber).Msg("Can't initialize mailer")
		utils.WriteResponseJSON(w, nil, mErr)
		return
	}

	utils.WriteResultJSON(w, subscriptions2.SubscribeAndSendEmail(r, mailer, subscriber, subscriptionsMap))
}

func ManageAPI_GET(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	secret, e := uuid.FromString(p["secret"])
	if e != nil {
		utils.WriteResponseJSON(w, nil, e)
		return
	}

	utils.WriteResultJSON(w, subscriptions2.GetSubscriptionsByManageCode(r, secret))
}

func ManageAPI_POST(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	secret, e := uuid.FromString(p["secret"])
	if e != nil {
		utils.WriteResponseJSON(w, nil, e)
		return
	}
	var req ApiRequest
	if err := binding.JSON.Bind(r.Request(), &req); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	subscriber := subscriptions2.Subscriber{
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Email:     req.Email,
	}
	subscriptionsMap := req.Topics

	mailer, mErr := transactionalMailer(r)
	if mErr != nil {
		logging.FromContext(r.Request().Context()).Error().Err(mErr).Interface("Subscriber", subscriber).Msg("Can't initialize mailer")
		utils.WriteResponseJSON(w, nil, mErr)
		return
	}

	utils.WriteResultJSON(w, subscriptions2.ManageExisting(r, mailer, secret, subscriber, subscriptionsMap))
}

func UnsubscribeGet(w http.ResponseWriter, r *shared.AppContext) {
	query := struct {
		Secret  uuid.UUID
		TopicId uuid.UUID
	}{}
	if err := binding.MapFromMaps(&query, r.Maps()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var message = make(map[string]string, 1)
	res := subscriptions2.Unsubscribe(r, query.Secret, query.TopicId)
	if res.IsError() {
		message["ErrorMessage"] = "Unable to unsubscribe from the selected topic at this time - please try again later."
		logging.FromContext(r.Request().Context()).Error().Err(res.Unwrap()).
			Interface("query", query).Msg("Unable to unsubscribe.")
	} else {
		message["SuccessMessage"] = "You have successfully unsubscribed from the selected topic."
		message["ManageLink"] = getRelativeManageLink(query.Secret.String())
	}

	html, err := publicServices.CompileTemplate(r, message, confirmHbs)
	showResult(w, r, err, html)
}

func ConfirmGet(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	secret, e := uuid.FromString(p["secret"])
	if e != nil {
		panic(e)
	}

	var html string
	var err error
	res := subscriptions2.ConfirmSubscriptions(r, secret)
	if res.IsSuccess() {
		html, err = publicServices.CompileTemplate(r, MessageViewModel{
			SuccessMessage: "Your email is successfully confirmed.",
			ManageLink:     getRelativeManageLink(secret.String()),
		}, confirmHbs)
	} else {
		html, err = publicServices.CompileTemplate(r, MessageViewModel{
			ErrorMessage: "We can't find your subscription or your code is expired.",
		}, confirmHbs)
	}

	showResult(w, r, err, html)
}

func GetManageLinkGet(w http.ResponseWriter, r *shared.AppContext) {
	html, err := publicServices.CompileTemplate(r, map[string]interface{}{
		"IsSystemPage": true,
	}, manageLinkHbs)
	showResult(w, r, err, html)
}

func GetManageLinkPost(w http.ResponseWriter, r *shared.AppContext) {
	var html string
	var err error
	email := r.Request().FormValue("Email")
	if !validating.Email(email) {
		html, err = publicServices.CompileTemplate(r, map[string]string{
			"ErrorMessage": "Please, enter a valid email address",
		}, manageLinkHbs)
		showResult(w, r, err, html)
		return
	}

	mailer, mErr := transactionalMailer(r)
	if mErr != nil {
		showResult(w, r, err, html)
		return
	}

	res := subscriptions2.SendManageLink(r, mailer, email)

	if res.IsSuccess() {
		html, err = publicServices.CompileTemplate(r, map[string]interface{}{
			"SuccessMessage": "Please, check your email to manage your subscriptions.",
			"IsSystemPage":   true,
		}, manageLinkHbs)
	} else {
		html, err = publicServices.CompileTemplate(r, map[string]interface{}{
			"ErrorMessage": "We couldn't find your email in our database. Please, try again.",
			"IsSystemPage": true,
		}, manageLinkHbs)
	}

	showResult(w, r, err, html)
}

func parseSubscriber(m map[string][]string) subscriptions2.Subscriber {
	return subscriptions2.Subscriber{
		FirstName: getFirst(m, "FirstName"),
		LastName:  getFirst(m, "LastName"),
		Email:     getFirst(m, "Email"),
	}
}

func parseSubscriptions(m map[string][]string) map[uuid.UUID]bool {
	subscriptionsMap := make(map[uuid.UUID]bool)
	for k, v := range m {
		id, err := uuid.FromString(k)
		if err != nil {
			continue
		}
		subscriptionsMap[id] = len(v) > 1
	}
	return subscriptionsMap
}

func getFirst(m map[string][]string, key string) string {
	if v, ok := m[key]; ok {
		return v[0]
	}
	return ""
}

func showResult(w http.ResponseWriter, r *shared.AppContext, err error, html string) {
	if err != nil {
		logging.FromContext(r.Request().Context()).Error().Err(err).Msg("error compiling template")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.Write([]byte(html))
}

func showErrorMessage(w http.ResponseWriter, r *shared.AppContext, message string) {
	html, err := publicServices.CompileTemplate(r, map[string]string{
		"ErrorMessage": message,
	}, messageHbs)
	showResult(w, r, err, html)
}

func transactionalMailer(r *shared.AppContext) (mailer.ITransactionalMailer, error) {
	m, e := mailer.NewFromRequest(r)
	if e != nil {
		return nil, e
	}
	return mailer.NewTransactionalMailer(r, m), nil
}

func getOverride(r *shared.AppContext, path string) (string, error) {
	contentChain, err := publicDataaccess.GetContentData(r, path)
	if err != nil {
		return "", err
	}
	if len(contentChain) == 0 {
		return "", gorm.ErrRecordNotFound
	}
	return publicServices.CompileHandlebars(r, publicServices.CompileHandlebarsParams{ContentChain: contentChain})
}

func getRelativeManageLink(secret string) string {
	return "/sys/notifications/manage/" + secret
}
