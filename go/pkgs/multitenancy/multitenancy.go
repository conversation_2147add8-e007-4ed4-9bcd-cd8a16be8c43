package multitenancy

import (
	"contentmanager/logging"
	"contentmanager/pkgs/config"
	"context"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"sync"
	"time"
)

const REFRESH_INTERVAL = 15 * time.Minute

type (
	Accessor interface {
		TenancyDB() *gorm.DB
		TenantDB(tenantID uuid.UUID) *gorm.DB
		TenantDBOrError(tenantID uuid.UUID) (*gorm.DB, error)
		SiteByID(tenantID, siteID uuid.UUID) (Site, error)
		SiteByHost(tenantID uuid.UUID, host string) (Site, error)
		TenantSites(tenantID uuid.UUID) ([]Site, error)
		Tenants() []Tenant
		TenantByID(tenantID uuid.UUID) (Tenant, error)
		TenantSiteIDsByHost(host string) (Domain, error)
		WithContext(ctx context.Context) Accessor
		TenantByAdminURL(host string) (Tenant, error)
		TenantIDByHost(host string) (uuid.UUID, error)
		GoogleAdminByEmailDomain(emailDomain string) (string, error)
	}
)

var (
	// never refreshed
	parentContext context.Context
	tenancyDB     *gorm.DB
	tenantDBs     SnapshotCache[uuid.UUID, struct {
		params DBParams
		db     *gorm.DB
	}]
	tenants                          SnapshotCache[uuid.UUID, Tenant] // tenants are "never" updated
	tenantAdminUrls                  SnapshotCache[string, Tenant]    // tenantAdminUrls are "never" updated
	googleAdminAccountsByEmailDomain SnapshotCache[string, string]

	// refreshed every REFRESH_INTERVAL
	idsByHost SnapshotCache[string, Domain]
	sitesByID SnapshotCache[uuid.UUID, Site]

	refreshInit sync.Once
)

func initNoCache(ctx context.Context, cfg config.AppConfig) {
	now := time.Now()

	if parentContext == nil {
		parentContext = ctx
	}

	var lastErr error
	for {
		lastErr = nil
		if err := initTenancyDB(ctx, cfg); err != nil {
			lastErr = err
			goto sleep
		}
		if err := initTenants(ctx); err != nil {
			lastErr = err
			goto sleep
		}

		logging.RootLogger().Info().Dur("duration", time.Since(now)).Msg("Multitenancy initialized")
		return

	sleep:
		logging.RootLogger().Error().Err(lastErr).Msg("Failed to initialize tenancy cache, retrying in 5 seconds")
		time.Sleep(5 * time.Second)
	}
}

func initWithCache(ctx context.Context, cfg config.AppConfig) {
	now := time.Now()
	if parentContext == nil {
		parentContext = ctx
	}

	var lastErr error
	for {
		lastErr = nil
		if err := initTenancyDB(ctx, cfg); err != nil {
			lastErr = err
			goto sleep
		}
		if err := initTenants(ctx); err != nil {
			lastErr = err
			goto sleep
		}
		if err := initSites(); err != nil {
			lastErr = err
			goto sleep
		}
		if err := initHosts(); err != nil {
			lastErr = err
			goto sleep
		}

		refreshInit.Do(func() {
			go func() {
				for {
					time.Sleep(REFRESH_INTERVAL)
					now := time.Now()
					if err := RefreshSnapshots(); err != nil {
						logging.RootLogger().Error().Err(err).Msg("Failed to refresh snapshots")
					} else {
						logging.RootLogger().Info().Dur("duration", time.Since(now)).Msg("Snapshots refreshed")
					}
				}
			}()
		})

		logging.RootLogger().Info().Dur("duration", time.Since(now)).Msg("Multitenancy cache initialized")
		return

	sleep:
		logging.RootLogger().Error().Err(lastErr).Msg("Failed to initialize tenancy cache, retrying in 5 seconds")
		time.Sleep(5 * time.Second)
	}
}

func RefreshSnapshots() error {
	if err := refreshTenants(); err != nil {
		return err
	}
	if err := refreshSites(); err != nil {
		return err
	}
	if err := refreshHosts(); err != nil {
		return err
	}
	return nil
}

func initTenancyDB(ctx context.Context, cfg config.AppConfig) error {
	if tenancyDB == nil {
		db, err := CreateDBConnection(DBParams{
			Context:  ctx,
			Host:     cfg.DbHost,
			Port:     cfg.DbPort,
			Server:   cfg.DbServer,
			User:     cfg.DbUser,
			Password: cfg.DbPassword,
		})
		if err != nil {
			return err
		}
		tenancyDB = db.WithContext(ctx)
	}
	return nil
}

func initTenants(ctx context.Context) error {
	if tenants != nil {
		return nil
	}

	// First initialize dependencies from tenancyDB
	tenantsMap, err := getTenants()
	if err != nil {
		return err
	}
	mappedAdminAccounts, err := getGoogleAdminAccountsByEmailDomain()
	if err != nil {
		return err
	}
	googleAdminAccountsByEmailDomain = NewSnapshotCache(mappedAdminAccounts)

	dbsMap := make(map[uuid.UUID]struct {
		params DBParams
		db     *gorm.DB
	}, len(tenantsMap))
	adminUrlMap := make(map[string]Tenant, len(tenantsMap))
	for _, tenant := range tenantsMap {
		params := DBParams{
			Context:  ctx,
			Host:     tenant.Host,
			Port:     "5432",
			Server:   tenant.Server,
			User:     tenant.DBUser,
			Password: tenant.DBPassword,
		}
		dbsMap[tenant.ID] = struct {
			params DBParams
			db     *gorm.DB
		}{params, nil}

		adminUrlMap[tenant.AdminURL] = tenant
	}

	tenants = NewSnapshotCache(tenantsMap)
	tenantDBs = NewSnapshotCache(dbsMap)
	tenantAdminUrls = NewSnapshotCache(adminUrlMap)

	return nil
}

func refreshTenants() error {
	tenantsMap, err := getTenants()
	if err != nil {
		return err
	}
	tenants.Refresh(tenantsMap)
	return nil
}

func getTenants() (map[uuid.UUID]Tenant, error) {
	var tt []Tenant
	if err := tenancyDB.Where("active").Find(&tt).Error; err != nil {
		return nil, err
	}
	tenantsMap := make(map[uuid.UUID]Tenant, len(tt))
	for _, tenant := range tt {
		tenantsMap[tenant.ID] = tenant
	}
	return tenantsMap, nil
}

type (
	GoogleDomainAdminAccount struct {
		EmailDomain       string
		AdminAccountEmail string
	}
)

func getGoogleAdminAccountsByEmailDomain() (map[string]string, error) {
	var emailsToAdminAccount = []GoogleDomainAdminAccount{}
	if err := tenancyDB.
		Raw("SELECT email_domain, admin_account_email FROM google_email_domains").
		Scan(&emailsToAdminAccount).
		Error; err != nil {
		return nil, err
	}
	var mapped = make(map[string]string, len(emailsToAdminAccount))
	for _, email := range emailsToAdminAccount {
		mapped[strings.ToLower(email.EmailDomain)] = strings.ToLower(email.AdminAccountEmail)
	}
	return mapped, nil
}

func initSites() error {
	if sitesByID != nil {
		return nil
	}

	sitesMap, err := getSites()
	if err != nil {
		return err
	}
	sitesByID = NewSnapshotCache(sitesMap)
	return nil
}

func refreshSites() error {
	sitesMap, err := getSites()
	if err != nil {
		return err
	}
	sitesByID.Refresh(sitesMap)
	return nil
}

func getSites() (map[uuid.UUID]Site, error) {
	var ss []Site
	if err := tenancyDB.Raw(`
        SELECT 
            s.*,
            ARRAY_AGG(sr.target_site_id) FILTER (WHERE sr.target_site_id IS NOT NULL) as hosts
        FROM site s
        LEFT JOIN site_relationship sr ON s.id = sr.source_site_id
		WHERE s.active
        GROUP BY s.id
    `).Scan(&ss).Error; err != nil {
		return nil, err
	}
	sitesMap := make(map[uuid.UUID]Site, len(ss))
	for _, site := range ss {
		sitesMap[site.ID] = site
	}
	return sitesMap, nil
}

func initHosts() error {
	if idsByHost != nil {
		return nil
	}

	idsMap, err := getDomains()
	if err != nil {
		return err
	}
	idsByHost = NewSnapshotCache(idsMap)
	return nil
}

func refreshHosts() error {
	idsMap, err := getDomains()
	if err != nil {
		return err
	}
	idsByHost.Refresh(idsMap)
	return nil
}

func getDomains() (map[string]Domain, error) {
	var domains []Domain
	if err := tenancyDB.Raw("SELECT tenant_id, site_id, domain.domain FROM domain WHERE active").Scan(&domains).Error; err != nil {
		return nil, err
	}
	idsMap := make(map[string]Domain, len(domains))
	for _, d := range domains {
		idsMap[strings.ToLower(d.Domain)] = d
	}
	return idsMap, nil
}
