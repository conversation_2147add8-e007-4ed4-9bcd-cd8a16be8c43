# Social authentication

## Endpoints
- `[HOST]/auth/:provider/callback` -- callback url for social provider. Single [HOST] with redirect for all websites.
- `/auth/:provider/login` -- initiate login with social provider
- for logout we are using `/api/v1/logout` endpoint

Implemented providers (see [addSAuth.go](addSAuth.go)) with [goth library](https://github.com/markbates/goth/blob/master/examples/main.go):
- `google` uses `github.com/markbates/goth/providers/google` implementation
- `microsoft` uses `github.com/markbates/goth/providers/microsoftonline` implementation
- `twitter` uses custom implementation to provide support for `state` param in callback url (see [twitterstate.go](providers/twitterstate/twitterstate.go))
- `yahoo` uses custom [yahoo.go](providers/yahoo/yahoo.go) implementation

Sign in links for UI to add to Master template:
```html
<a href="/sys/auth/google/login">Sign in with Google</a><br>
<a href="/sys/auth/microsoftonline/login">Sign in with Microsoft</a><br>
<a href="/sys/auth/twitter/login">Sign in with Twitter</a><br>
<a href="/sys/auth/yahoo/login">Sign in with Yahoo</a><br>
<hr />
<a href="/sys/auth/whoami">Current user</a><br>
```
## Configuration
```golang
package models
type Service struct {
    // ...
  
    // Social Auth
    SocAuthHost            string // host for callback url (!including protocol!)
    SocAuthTwitterID       string
    SocAuthTwitterSecret   string
    SocAuthGoogleID        string
    SocAuthGoogleSecret    string
    SocAuthMicrosoftID     string
    SocAuthMicrosoftSecret string
    SocAuthYahooID         string
    SocAuthYahooSecret     string
}
```
