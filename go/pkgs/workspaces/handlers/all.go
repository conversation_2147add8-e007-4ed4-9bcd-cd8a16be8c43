package handlers

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
)

func GetAllWorkspaces(r *shared.AppContext) result.Result[[]string] {
	res := struct {
		WorkspaceList dbDriver.PgStringArray `gorm:"type:text[]"`
	}{[]string{}}

	if err := r.TenantDatabase().Table("mv_active_workspaces").First(&res).Error; err != nil {
		return result.Error(err, []string{})
	}
	return result.Success([]string(res.WorkspaceList))
}
