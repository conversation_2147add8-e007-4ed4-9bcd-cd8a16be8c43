package csp

import "fmt"

type Directives struct {
	StyleSrc  string
	ScriptSrc string
	FontSrc   string
	FrameSrc  string
	Content   string
}

/*
TODO =>

	Img-Src
	Connect-Src
	consider adding 'report-sample' (e.g script-src 'report-sample' ...) -  This will include a portion of the offending code in the report.
	(?) report-to csp-endpoint

Style-Src [self]
  - 'self' is ignored if hashes or a nonce exist in the source expression list
  - 'self' is considered if 'unsafe-inline' is in the ScriptSrc, as hashes & nonce are not added to directive.
  - it is also a good fallback for older browsers (if they are still used) that aren't CSP level 2 | 3
*/

func (d Directives) String() string {
	return fmt.Sprintf(`
		default-src * data: ; 
		script-src 'strict-dynamic' %s ; 
		style-src 'self' 'unsafe-hashes' %s ;
		font-src 'self' data: %s ;
		frame-src 'self' %s ;
		object-src 'none' ;
		base-uri 'none' ;
	`, d.ScriptSrc, d.StyleSrc, d.FontSrc, d.FrameSrc)
}

func (d Directives) isEmpty() bool {
	return d.FontSrc == "" && d.StyleSrc == "" && d.ScriptSrc == "" && d.FrameSrc == "" && d.Content == ""
}
