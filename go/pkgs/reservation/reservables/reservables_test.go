package reservables

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation/shared"
	"strings"
	"testing"
)

func Test_Compilable(t *testing.T) {
	cases := []struct {
		reservable shared.Reservable
		parts      int
	}{
		{&content.Content{}, 3},
		{&commonModels.Content{}, 3},
	}

	for _, c := range cases {
		t.Run(c.reservable.ReservationKey(), func(t *testing.T) {
			parts := strings.Split(c.reservable.ReservationKey(), "::")
			if len(parts) != c.parts {
				t.<PERSON>("Expected %d parts, got %d for reservable %T", c.parts, len(parts), c.reservable)
			}
		})
	}
}
