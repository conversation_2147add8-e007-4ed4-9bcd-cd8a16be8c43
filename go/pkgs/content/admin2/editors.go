package admin2

import (
	"contentmanager/library/shared/result"
	"github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type ContentEditor struct {
	ID   uuid.UUID `json:"id"`
	Name string    `json:"name"`
}

func GetContentEditors(db *gorm.DB) result.Result[[]ContentEditor] {
	res := []ContentEditor{}
	err := db.Raw(" " +
		"select distinct ids.id, concat(account.firstname, ' ', account.lastname) as name from ( " +
		"	select owner as id from content where owner is not null " +
		"	union " +
		"	select publisher as id from content where publisher is not null " +
		") as ids " +
		"join account on ids.id = account.id " +
		"order by name ",
	).Scan(&res).Error
	if err != nil {
		return result.Error(err, res)
	}
	return result.Success(res)
}
