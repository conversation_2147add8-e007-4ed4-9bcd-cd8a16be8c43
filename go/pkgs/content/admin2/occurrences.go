package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/content/events"
	"time"
)

type OccurrencesParams struct {
	Schedule events.Schedule
	From     time.Time
	To       time.Time
}

func Occurrences(r *shared.AppContext, params OccurrencesParams) result.Result[[]time.Time] {
	loc, err := time.LoadLocation(r.Timezone())
	if err != nil {
		return result.ErrorT[[]time.Time](err)
	}
	occ, err := events.GetOccurrences(params.Schedule, loc, params.From, params.To)
	if err != nil {
		return result.ErrorT[[]time.Time](err)
	}
	return result.Success(occ)
}
