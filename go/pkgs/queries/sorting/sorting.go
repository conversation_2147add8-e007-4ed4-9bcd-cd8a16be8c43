package sorting

import (
	uuid "github.com/satori/go.uuid"
	"log"
	"reflect"
	"sort"
	"strings"
	"time"
)

// Sorting specifies one field-based sorting criterion.
type Sorting struct {
	FieldName string // e.g. "Age" or "User.Name"
	Direction string // "asc" or "desc"
}

// Sort sorts the slice `items` according to the list of sorting criteria.
// It returns the sorted slice (the sort is done in-place on the original slice).
func Sort[T any](items []T, sortings []Sorting) []T {
	// If slice is nil or length < 2, no need to sort
	if len(items) < 2 {
		return items
	}

	// Preprocess and validate sorting criteria
	type fieldPath struct {
		indexPath []int        // reflect index path to the field
		fieldType reflect.Type // type of the field at the end of the path
		desc      bool         // true if sort direction is descending
	}
	var criteria []fieldPath

	// Get the reflect Type of the slice elements (dereference pointer if needed)
	elemType := reflect.TypeOf(items[0])
	if elemType.Kind() == reflect.Ptr {
		elemType = elemType.Elem() // support pointer-to-struct slices
	}

	for _, s := range sortings {
		if s.FieldName == "" {
			continue // skip empty field name
		}
		// Normalize direction string
		dir := strings.ToLower(s.Direction)
		var descending bool
		if dir == "asc" {
			descending = false
		} else if dir == "desc" {
			descending = true
		} else {
			log.Printf("Warning: invalid sort direction %q for field %q – skipping criterion", s.Direction, s.FieldName)
			continue
		}
		// Find the nested field by name path
		names := strings.Split(s.FieldName, ".")
		t := elemType
		var indexPath []int
		valid := true
		for _, name := range names {
			if t.Kind() == reflect.Ptr {
				// If current type is pointer, we navigate into its element
				t = t.Elem()
			}
			if t.Kind() != reflect.Struct {
				valid = false
				break
			}
			sf, ok := t.FieldByName(name)
			if !ok {
				log.Printf("Warning: field %q not found in type %s – skipping criterion", s.FieldName, elemType.Name())
				valid = false
				break
			}
			// Check if field is exported (PkgPath is empty for exported fields)
			if sf.PkgPath != "" {
				log.Printf("Warning: field %q is not exported – skipping criterion", s.FieldName)
				valid = false
				break
			}
			// Append the index path for this field
			indexPath = append(indexPath, sf.Index...)
			// Update t to the field's type for next iteration (for nested path)
			t = sf.Type
		}
		if !valid {
			continue
		}

		if t.Kind() == reflect.Ptr {
			t = t.Elem()
		}

		// At this point, indexPath leads to the target field, and t is the field type
		criteria = append(criteria, fieldPath{indexPath: indexPath, fieldType: t, desc: descending})
	}

	if len(criteria) == 0 {
		// No valid criteria to sort by
		return items
	}

	// Perform the sort using sort.SliceStable for stability
	sort.SliceStable(items, func(i, j int) bool {
		vi := reflect.ValueOf(items[i])
		vj := reflect.ValueOf(items[j])
		if vi.Kind() == reflect.Ptr {
			vi = vi.Elem()
		}
		if vj.Kind() == reflect.Ptr {
			vj = vj.Elem()
		}

		for _, crit := range criteria {
			// Navigate to the field
			fi := vi.FieldByIndex(crit.indexPath)
			fj := vj.FieldByIndex(crit.indexPath)

			// Handle pointer fields safely
			if fi.Kind() == reflect.Ptr || fj.Kind() == reflect.Ptr {
				// If both are pointers, compare nil-ness first
				if fi.Kind() == reflect.Ptr && fi.IsNil() && fj.Kind() == reflect.Ptr && !fj.IsNil() {
					return !crit.desc // for ascending: nil comes first
				}
				if fj.Kind() == reflect.Ptr && fj.IsNil() && fi.Kind() == reflect.Ptr && !fi.IsNil() {
					return crit.desc
				}
				// If one is a pointer (and non-nil), dereference it
				if fi.Kind() == reflect.Ptr && !fi.IsNil() {
					fi = fi.Elem()
				}
				if fj.Kind() == reflect.Ptr && !fj.IsNil() {
					fj = fj.Elem()
				}
			}

			// Now compare based on the field type
			switch crit.fieldType.Kind() {
			case reflect.Interface:
				// Extract the underlying values
				iv := fi.Interface()
				jv := fj.Interface()

				// Handle nil values first
				if iv == nil && jv == nil {
					continue
				}
				if iv == nil {
					return !crit.desc // decide if nil comes first
				}
				if jv == nil {
					return crit.desc
				}

				compare := compareInterfaces(iv, jv, crit.desc)
				if compare == 0 {
					continue
				}
				return compare == -1

			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				iv, jv := fi.Int(), fj.Int()
				if iv == jv {
					continue
				}
				if crit.desc {
					return iv > jv
				}
				return iv < jv
			case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
				iv, jv := fi.Uint(), fj.Uint()
				if iv == jv {
					continue
				}
				if crit.desc {
					return iv > jv
				}
				return iv < jv
			case reflect.Float32, reflect.Float64:
				iv, jv := fi.Float(), fj.Float()
				if iv == jv {
					continue
				}
				if crit.desc {
					return iv > jv
				}
				return iv < jv
			case reflect.String:
				iv, jv := fi.String(), fj.String()
				if iv == jv {
					continue
				}
				if crit.desc {
					return iv > jv
				}
				return iv < jv
			case reflect.Bool:
				bi, bj := fi.Bool(), fj.Bool()
				if bi == bj {
					continue
				}
				if crit.desc {
					// Descending: true comes before false
					return bi && !bj
				}
				// Ascending: false comes before true
				return !bi && bj
			default:
				// Special case for time.Time
				if crit.fieldType == reflect.TypeOf(time.Time{}) {
					ti := fi.Interface().(time.Time)
					tj := fj.Interface().(time.Time)
					if ti.Equal(tj) {
						continue
					}
					if crit.desc {
						return ti.After(tj)
					}
					return ti.Before(tj)
				}
				// Unsupported type: treat as equal and continue to the next criterion
				continue
			}
		}
		// If all criteria are equal, maintain the original order
		return false
	})

	return items
}

func compareInterfaces(iv, jv any, desc bool) int {
	switch i := iv.(type) {
	case *int:
		j, ok := jv.(*int)
		if !ok {
			return 0 // or handle the error case appropriately
		}
		// Compare the values pointed to by i and j.
		if *i == *j {
			return 0
		}
		if (*i < *j && !desc) || (*i > *j && desc) {
			return -1
		}
		return 1
	case int:
		j, ok := jv.(int)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		if (i < j && !desc) || (i > j && desc) {
			return -1
		}
		return 1
	case time.Time:
		j, ok := jv.(time.Time)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		if (i.Before(j) && !desc) || (i.After(j) && desc) {
			return -1
		}
		return 1
	case *time.Time:
		j, ok := jv.(*time.Time)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		if (i.Before(*j) && !desc) || (i.After(*j) && desc) {
			return -1
		}
		return 1
	case string:
		j, ok := jv.(string)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		if (i < j && !desc) || (i > j && desc) {
			return -1
		}
		return 1
	case *string:
		j, ok := jv.(*string)
		if !ok {
			return 0
		}
		if *i == *j {
			return 0
		}
		if (*i < *j && !desc) || (*i > *j && desc) {
			return -1
		}
		return 1
	case float64:
		j, ok := jv.(float64)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		if (i < j && !desc) || (i > j && desc) {
			return -1
		}
		return 1
	case *float64:
		j, ok := jv.(*float64)
		if !ok {
			return 0
		}
		if *i == *j {
			return 0
		}
		if (*i < *j && !desc) || (*i > *j && desc) {
			return -1
		}
		return 1
	case bool:
		j, ok := jv.(bool)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		// For bools: false < true
		if (!i && j && !desc) || (i && !j && desc) {
			return -1
		}
		return 1
	case *bool:
		j, ok := jv.(*bool)
		if !ok {
			return 0
		}
		if *i == *j {
			return 0
		}
		if (!*i && *j && !desc) || (*i && !*j && desc) {
			return -1
		}
		return 1
	case uuid.UUID:
		j, ok := jv.(uuid.UUID)
		if !ok {
			return 0
		}
		if i == j {
			return 0
		}
		comparison := strings.Compare(i.String(), j.String())
		if comparison == 0 {
			return 0
		}
		if (comparison < 0 && !desc) || (comparison > 0 && desc) {
			return -1
		}
		return 1
	}
	return 0
}
