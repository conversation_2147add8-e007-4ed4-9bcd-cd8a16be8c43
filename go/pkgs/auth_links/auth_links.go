package auth_links

import (
	"contentmanager/library/utils/urls"
	tenantModels "contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/cryptor"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

const urlValidityDuration = 12 * 30 * 24 * time.Hour

// AuthenticateAccountFromToken assigns only basic account info, no groups processed,
// because for the use case we need only PrivacyLevel.
func AuthenticateAccountFromToken(req *http.Request, db *gorm.DB) (*tenantModels.Account, error) {
	encryptedToken := req.URL.Query().Get("token")

	if encryptedToken == "" {
		return nil, nil
	}

	decrypted, err := cryptor.Decrypt(encryptedToken)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt token: %w", err)
	}

	parts := strings.Split(decrypted, "|")
	if len(parts) != 2 {
		return nil, errors.New("invalid token format")
	}

	email := parts[0]

	expirationUnix, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse expiration date: %w", err)
	}
	expiration := time.Unix(expirationUnix, 0)

	// Check if the token has expired
	if time.Now().After(expiration) {
		return nil, errors.New("token has expired")
	}

	var account *tenantModels.Account
	if err := db.Where("lower(email) = lower(?)", email).First(&account).Error; err != nil {
		return nil, err
	}

	if !account.Active {
		return nil, errors.New("Your account is not active. ")
	}

	return account, nil
}

// AddTokenToURL adds the query parameter `token` to the provided URL. If the request is anonymous, no token will be added.
func AddTokenToURL(accountEmail string, u *url.URL) (*url.URL, error) {
	if accountEmail == "" {
		return u, nil
	}

	token, err := GetToken(accountEmail)
	if err != nil {
		return nil, err
	}

	u = urls.ReplaceGetParam(u, "token", token)
	return u, nil
}

func GetToken(accountEmail string) (string, error) {
	if accountEmail == "" {
		return "", errors.New("[GetToken] The Email is required. ")
	}

	expiration := time.Now().Add(urlValidityDuration).Unix()
	token := fmt.Sprintf("%s|%d", accountEmail, expiration)
	return cryptor.Encrypt(token)
}
