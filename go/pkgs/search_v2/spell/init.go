package spell

import (
	"bufio"
	"embed"
	"log"
	"strconv"
	"strings"
)

//go:embed data/frequency_dictionary_en_82_765.txt
var freqDictFS embed.FS

var s *Spell

func init() {
	s = New()

	// Open the embedded frequency dictionary file
	file, err := freqDictFS.Open("data/frequency_dictionary_en_82_765.txt")
	if err != nil {
		log.Fatalf("Failed to open embedded file: %v", err)
	}
	defer file.Close()

	// Use a buffered scanner to read the file line by line
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()

		// Split the line into word and frequency
		parts := strings.Fields(line)
		if len(parts) != 2 {
			log.Printf("Invalid line format: %s", line)
			continue
		}

		word := parts[0]
		freqStr := parts[1]

		// Convert frequency string to integer
		freq, err := strconv.ParseUint(freqStr, 10, 64)
		if err != nil {
			log.Printf("Invalid frequency for word %s: %v", word, err)
			continue
		}

		// Add the entry to the spell checker dictionary
		ok, err := s.AddEntry(Entry{
			Frequency: freq,
			Word:      word,
		})
		if !ok {
			log.Printf("Failed to add entry for word %s: %v", word, err)
		}
		if err != nil {
			log.Printf("Error adding entry for word %s: %v", word, err)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Fatalf("Error reading embedded file: %v", err)
	}
}
