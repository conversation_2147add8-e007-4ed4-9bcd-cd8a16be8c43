package suggestions

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	uuid "github.com/satori/go.uuid"
)

const MIN_SEARCH_LENGTH = 3

type (
	Query struct {
		SearchText string
		AllSites   bool
	}

	SuggestionViewModel struct {
		Phrase string
		PID    uuid.UUID // Suggestion ID
	}
)

// EXISTS and unnest are impossible to optimize. If we have a lot of suggestions, this query will be slow.
// To optimize this query, we need to create a new table with keywords.
const sql = `
	SELECT * 
	FROM search_suggestions sp
	WHERE sp.active = true 
	  AND (sp.publish_at IS NOT NULL AND sp.publish_at <= now())
	  AND (sp.expire_at IS NULL OR sp.expire_at > now())
      
      AND (
		(sp.exact_match = true AND EXISTS (
			SELECT 1 
			FROM unnest(sp.keywords) as k 
			WHERE lower(@SearchText) ~ ('\y' || regexp_replace(lower(k), '\s+', '\s+') || '\y')
			LIMIT 1
		))
		OR
		(sp.exact_match = false AND row_tsvector @@ websearch_to_tsquery('english', @SearchText))	
      )
;
`

func MatchSuggestions(r *shared.AppContext, query Query) result.Result[[]SuggestionViewModel] {
	suggestions := []SearchSuggestion{}
	res := []SuggestionViewModel{}

	if len(query.SearchText) < MIN_SEARCH_LENGTH {
		return result.Success(res)
	}

	params := map[string]interface{}{
		"SearchText": query.SearchText,
	}
	if err := r.TenantDatabase().Raw(sql, params).Scan(&suggestions).Error; err != nil {
		return result.Error(err, res)
	}

	for _, suggestion := range suggestions {
		for _, item := range suggestion.Items {
			res = append(res, SuggestionViewModel{
				Phrase: item,
				PID:    suggestion.ID,
			})
		}
	}

	return result.Success(res)
}
