package admin

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/auth"
	models "contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/search_v2"
	"contentmanager/pkgs/search_v2/suggestions"
	"contentmanager/tests"
	"context"
	uuid "github.com/satori/go.uuid"
	"strings"
	"testing"
	"time"
)

var (
	now     = time.Now()
	inMonth = now.AddDate(0, 1, 0)
	since   = now.AddDate(0, -1, 0)

	keywords0 = []string{"3 year", "3 year education plan", "3-year", "3-year ed plan", "cbe 3 year plan", "cbe three-year education plan", "ed plan", "education plan", "three year ed", "three year education", "three year education plan", "three-year ed", "three-year ed plan", "three-year education", "three-year education plan"}

	keywords1 = []string{"early french imersion", "french immersion", "german", "german bilingual", "immersion", "late french immersion", "mandarin", "spanish", "spanish bilingual"}

	keywords2 = []string{"elibrary", "library"}

	keywords3 = []string{"email", "e-mail", "human resources phone number", "phone", "phone number", "phone numbers"}

	pp = []SuggestionDTO{
		getSuggestion(keywords0),
		getSuggestion(keywords1),
		getSuggestion(keywords2),
		getSuggestion(keywords3),
	}
)

func Test_Suggestions(t *testing.T) {
	ctx := context.Background() // tests.InitLogging("Test_Suggestions") //
	db, dispose := tests.InitTenantDB()
	defer dispose()

	r := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
		TenantDB: db.WithContext(ctx),
		Identity: &models.Account{
			ID: uuid.FromStringOrNil("********-0000-0000-0000-************"),
		},
	})

	// Create suggestions
	ids := []uuid.UUID{}
	for _, p := range pp {
		res := CreateSuggestion(r, p)
		ids = append(ids, res.Data)
		if !res.Success {
			t.Fatalf("Error saving suggestion: %v", res.Unwrap())
		}
	}

	// Check exact match
	res := suggestions.MatchSuggestions(r, suggestions.Query{SearchText: "library", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching suggestions: %v", res.Unwrap())
	}
	if len(res.Data) != 1 {
		t.Fatalf("Expected 1 suggestion, got %d", len(res.Data))
	}

	// Check non-exact match (should not match)
	res = suggestions.MatchSuggestions(r, suggestions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching suggestions: %v", res.Unwrap())
	}
	if len(res.Data) != 0 {
		t.Fatalf("Expected 1 suggestion, got %d", len(res.Data))
	}

	// Update suggestion to be non-exact match
	librarySuggestion := pp[2]
	librarySuggestion.ExactMatch = false
	resUpdate := UpdateSuggestion(r, ids[2], librarySuggestion)
	if !resUpdate.Success {
		t.Fatalf("Error updating suggestion: %v", resUpdate.Unwrap())
	}

	// Check if suggestion is updated
	updated := GetSuggestion(r, ids[2])
	if !updated.Success {
		t.Fatalf("Error getting suggestion: %v", updated.Unwrap())
	}
	if updated.Data.ExactMatch {
		t.Fatalf("Expected suggestion to be non-exact match")
	}

	// Check non-exact match (should match)
	res = suggestions.MatchSuggestions(r, suggestions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching suggestions: %v", res.Unwrap())
	}
	if len(res.Data) != 1 {
		t.Fatalf("Expected 1 suggestion, got %d", len(res.Data))
	}

	// Delete all suggestions
	for _, id := range ids {
		resDelete := DeleteSuggestion(r, id)
		if !resDelete.Success {
			t.Fatalf("Error deleting suggestion: %v", resDelete.Unwrap())
		}
	}

	// Check if all suggestions are deleted. No matches should be found
	res = suggestions.MatchSuggestions(r, suggestions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching suggestions: %v", res.Unwrap())
	}
	if len(res.Data) != 0 {
		t.Fatalf("Expected 0 suggestion, got %d", len(res.Data))
	}

}

func getSuggestion(keywords []string) SuggestionDTO {
	keywordsStr := strings.Join(keywords, "; ")
	return SuggestionDTO{
		KeywordSearch:  search_v2.KeywordSearch{Keywords: keywords, ExactMatch: true},
		PublishPeriod:  content.PublishPeriod{PublishAt: &now, ExpireAt: &inMonth},
		TenantWideBase: auth.TenantWideBase{},
		Secured:        suggestions.Secured{},
		Name:           keywordsStr,
		Items:          []string{keywordsStr},
	}
}
