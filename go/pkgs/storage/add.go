package storage

import (
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	publicServices "contentmanager/library/tenant/public/services"
	"contentmanager/library/utils"
	"errors"
	uuid "github.com/satori/go.uuid"
	"io"
	"net/http"
	"time"
)

func AddStorage(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("", func(router httpService.Router) {
		router.Get("/images/:filename", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				Filename string `binding:"required"`
			}
			FromQuery struct {
				Crop string
			}
		}) {
			serveImage(w, r, params.FromPath.Filename, "full", params.FromQuery.Crop)
		})

		router.Get("/thumbnails/:filename", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				Filename string `binding:"required"`
			}
		}) {
			serveImage(w, r, params.FromPath.Filename, "thumbnail", "")
		})

		router.Get("/documents/:id/:filename", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID       uuid.UUID `binding:"required"`
				Filename string    `binding:"required"`
			}
		}) {
			attachment, err := GetDocument(r, params.FromPath.ID)
			if err != nil {
				r.Logger().Error().Err(err).Msg("[GetDocument] error getting public document")
				if utils.IsErrorNotFound(err) {
					w.WriteHeader(http.StatusNotFound)
					publicServices.NotFound(w, r)
				} else if errors.Is(err, utils.PermissionRequiredErrorMsg) {
					w.WriteHeader(http.StatusForbidden)
					publicServices.AccessDenied(w, r)
					return
				} else if utils.IsErrorS3Timeout(err) {
					w.WriteHeader(http.StatusRequestTimeout)
					publicServices.Whoops(w, r)
				} else {
					http.Error(w, err.Error(), http.StatusBadRequest)
				}
				return
			}

			serveFile(w, r, attachment)
		})

	}, bindauth.BindParamsMiddleware())

	return r
}

func serveImage(w http.ResponseWriter, r *shared.AppContext, fileName, size, crop string) {
	res, err := GetImage(r, fileName, size, crop)
	if err != nil {
		http.NotFound(w, r.Request())
		return
	}
	serveFile(w, r, res)
}

func serveFile(w http.ResponseWriter, r *shared.AppContext, attachment *File) {
	defer func() {
		if err := attachment.Reader.Close(); err != nil {
			r.Logger().Error().Err(err).Msg("Failed to close attachment reader")
		}
	}()

	w.Header().Set("Content-Type", attachment.ContentType)
	w.Header().Set("Last-Modified", time.Now().UTC().Format(http.TimeFormat))

	_, err := io.Copy(w, attachment.Reader)
	if err != nil {
		r.Logger().Error().Err(err).Msg("Failed to copy image to response")
	}
}
