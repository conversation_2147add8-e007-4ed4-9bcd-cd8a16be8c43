import React, { useMemo } from 'react'
import { CustomMenuItem } from './CustomMenu'
import { notify } from '../../../helpers'
import { hideNowContentQuery } from '@/pkgs/content/queries'
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';

export const HideNowCustomMenuItem = ({ value, onChange, disabled }) => {
    const isHidden = useMemo(() => {
        const expireAt = value?.expireAt || value?.ExpireAt

        return expireAt && Date.parse(expireAt) < Date.now()
    }, [value])

    const hideNow = async () => {
        try {
            await hideNowContentQuery(value?.ID || value?.id, value?.Workspace)
            onChange?.()
        } catch (e) {
            console.error(e)
            notify('Whoops - Something went wrong, find more details in the console.', 'error')
        }
    }

    if (value == null) {
        return null
    }
    return (
        <CustomMenuItem
            text={'Hide Now'}
            onClick={hideNow}
            disabled={disabled || isHidden}
        >
            {< VisibilityOffIcon />}
        </CustomMenuItem>
    )
}
