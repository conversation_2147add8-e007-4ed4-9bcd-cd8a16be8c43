import lodash from 'lodash'

export default function replaceObjectValueWith(_object, matcher: (key: string, value: any) => boolean, newValue: any) {
    if (!_object) return null

    const newObj = lodash.clone(_object)
    const stack = [[newObj, Object.keys(newObj)]]

    while (stack.length) {
        const current = stack.pop()
        if (!current) return
        const [currentObj, currentObjKeys] = current
        for (const key of currentObjKeys) {
            if (typeof currentObj[key] === 'object' && currentObj[key] !== null) {
                const dd = [currentObj[key], Object.keys(currentObj[key])]
                stack.push(dd)
            } else if (matcher(key, currentObj[key])) {
                currentObj[key] = newValue
            }
        }
    }

    return newObj
}
