import { useState, useEffect, RefObject } from 'react'
import { useContentValidators } from '../validators'
import { Content, ContentCreate } from '../types'
import { notify } from '../../../helpers'
import { ICustomMenuItem } from '@/pkgs/system/menu-builder/queries'

type useStructuredContentValidatorsProps = {
    componentsToValidate: Array<keyof Content>
    state: Content | ContentCreate | undefined
    formRendererRef?: RefObject<any>
    cdi?: ICustomMenuItem | undefined | null
}

export const useStructuredContentValidators = ({
    componentsToValidate,
    state,
    formRendererRef,
    cdi
}: useStructuredContentValidatorsProps) => {
    const contentValidators = useContentValidators(cdi)
    const [stateLoaded, setStateLoaded] = useState(false)
    const [errors, setErrors] = useState<
        Partial<Record<keyof Content, string>> | Partial<Record<keyof ContentCreate, string>>
    >({})

    useEffect(() => {
        if (!state || stateLoaded) return
        setStateLoaded(true)
    }, [state, stateLoaded])

    for (const cp of componentsToValidate) {
        useEffect(() => {
            if (!state || !stateLoaded) return

            setErrors((p) => ({ ...p, ...contentValidators[cp](state as Content) }))
        }, [state?.[cp]])
    }

    const validateAll = () => {
        if (!state) {
            return false
        }

        const validationErrors: Partial<Record<keyof Content, string>> = {}
        for (const cp of componentsToValidate) {
            Object.assign(validationErrors, contentValidators[cp](state as Content))
        }

        // Perform formRendererRef validation if it is provided and current
        if (formRendererRef?.current && typeof formRendererRef.current.validateAndReturnErrors === 'function') {
            const [components] = formRendererRef.current.validateAndReturnErrors()
            console.log('FormRenderer errors', components)
            if (Array.isArray(components) && components.length) {
                validationErrors.Data = 'Structure Form has errors'
            }
        }

        setErrors(validationErrors)

        if (Object.keys(validationErrors).some((key) => !!validationErrors[key])) {
            notify(
                <>
                    <div>
                        <h4>Errors found</h4>
                        <ul>
                            {Object.keys(validationErrors)
                                .filter((key) => !!validationErrors[key])
                                .map((key) => (
                                    <li key={key}>
                                        <strong>{key}</strong>:&nbsp;
                                        {validationErrors[key]}
                                    </li>
                                ))}
                        </ul>
                    </div>
                </>,
                'error'
            )
            return false
        }

        return true
    }

    return { validateAll, errors, setErrors }
}
