import React, { useState } from 'react'
import {
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    Stack,
    Switch
} from '@mui/material'
import TextField from '@mui/material/TextField'
import { BaseTemplate, TemplateSelector } from './components/TemplateSelector'
import { StructureSelector } from '../../structure/StructureSelector'
import Button from '@mui/material/Button'
import { Content, ContentCreate, ContentLike, getAllContentLikeContent } from '../types'
import { useStructuredContentValidators } from './useStructuredContentValidators'
import { httpPost } from '@/common/client'
import { undefined, z } from 'zod'
import { notify } from '../../../helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { getFeaturedMenuItemContentEditorPath, useAppNavigation } from '@/app/useAppNavigation'
import { ContentTypeSelector } from '../ContentTypeSelector'
import { BASE } from '@/common/constants'
import { typeToEntityScope } from '../../auth/entityScope'
import { useAppContext } from '../../auth/atoms'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { TagType } from '@/pkgs/system/tags/types'
import { SitesSelectorComponent } from '@/common/components/selectors/SitesSelectorComponent'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'

interface CreateStructuredContentProps {
    open: boolean
    onClose: () => void
    onImport?: () => void
    onCreate?: (id: string, content: Content) => void
    availableTypes?: ContentLike[]
}

export const CreateStructuredContent = ({
    open,
    onClose,
    onImport,
    onCreate = () => {},
    availableTypes = getAllContentLikeContent()
}: CreateStructuredContentProps) => {
    const { data: cdi } = useURLParamMenuItem()
    const appContext = useAppContext()
    const validatorOverrides = appContext.getConfig('ContentEditorValidatorOverrides') as Record<string, any>

    const now = Date.now()

    const { navigateTo } = useAppNavigation()
    const [loading, setLoading] = useState(false)
    const [navigateToEditor, setNavigateToEditor] = useState(true)
    const [selectedTemplate, setSelectedTemplate] = useState<BaseTemplate | undefined>()

    const [state, setState] = useState<Content>({
        // @ts-ignore
        ID: null,
        Workspace: 'live',
        Type: availableTypes[0],
        PageLayout: 'DCT',
        Title: '',
        Route: '',
        Sites: [],
        Path: '',
        StructureID: null,
        ExpireAt: null,
        PublishAt: null,
        // To satisfy the Content read interface
        Meta: {},
        Data: {},
        Created: now as any,
        Updated: now as any,
        Active: true,
        Deleted: null,
        MediaID: null,
        Owner: '',
        PrivacyLevel: 0,
        Publisher: '',
        Tags: [],
        Settings: {}
    })

    const { validateAll, errors } = useStructuredContentValidators({
        componentsToValidate: ['Sites', 'Route', 'Title', 'Path', 'StructureID', 'Tags'],
        state,
        cdi
    })

    React.useEffect(() => {
        const titlePart = state.Title.replace(/[^a-zA-Z0-9]+/g, '-').toLowerCase()
        const suffix = new Date()
            .toISOString()
            .split('.')[0]
            .replaceAll(/[^0-9]+/g, '')

        switch (state.Type) {
            case 'alert':
                setState({ ...state, Route: '' })
                break
            case 'news':
                setState({ ...state, Route: `/news/${titlePart}-${suffix}` })
                break
            case 'event':
                setState({ ...state, Route: `/event/${titlePart}-${suffix}` })
                break
            default:
                setState({ ...state, Route: `/${titlePart}` })
        }
    }, [state.Title, state.Type])

    const allowedStructures = Boolean(cdi?.Structures)
        ? selectedTemplate?.Structures?.filter((s) => cdi?.Structures?.includes(s)) || []
        : selectedTemplate?.Structures || []

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth={'md'}>
            <div className={'flex-row-align-center'}>
                <DialogTitle style={{ marginRight: '0.5rem' }}>Create {state.Type}</DialogTitle>
                {/* <WhatsNew link={'/what-is-a-structure?hash=doL3doYXQtaXMtYS1zdHJ1Y3R1cmU='} /> */}
            </div>

            <DialogContent>
                <Stack spacing={2} direction={'column'}>
                    <FormControl fullWidth sx={{ my: 1 }}>
                        <TextField
                            data-testid='add-page-title'
                            label='Title'
                            required
                            style={{ width: '100%' }}
                            value={state.Title || ''}
                            onChange={(v) => state && setState({ ...state, Title: v.target.value })}
                            error={!!errors.Title}
                        />
                    </FormControl>

                    {availableTypes.length > 1 && (
                        <FormControl fullWidth sx={{ my: 1 }}>
                            <ContentTypeSelector
                                value={state.Type as ContentLike}
                                onChange={(v) => setState({ ...state, Type: v })}
                                availableTypes={availableTypes}
                            />
                        </FormControl>
                    )}
                    <FormControl fullWidth sx={{ m: 0 }}>
                        <SitesSelectorComponent
                            value={state}
                            onChange={(v) =>
                                setState({
                                    ...state,
                                    Sites: v.Sites,
                                    DepartmentID: v.DepartmentID
                                })
                            }
                            contentType={typeToEntityScope(state.Type)}
                            error={errors.Sites}
                        />
                    </FormControl>
                    <FormControl fullWidth sx={{ my: 1 }}>
                        <TemplateSelector
                            required
                            ownerID={''}
                            path={state.Path}
                            onChange={(path, tpl) => {
                                setState({ ...state, Path: path })
                                setSelectedTemplate(tpl)
                            }}
                            templateType={'all'}
                            classifications={[state.Type]}
                            error={errors.Path}
                            ignoreNotFound={true}
                            allowedTemplateIDs={cdi?.Templates || []}
                        />
                    </FormControl>
                    <FormControl fullWidth sx={{ my: 1 }}>
                        <StructureSelector
                            required
                            value={state.StructureID}
                            onChange={(v, s) => {
                                setState({ ...state, StructureID: v || null })
                            }}
                            error={errors.StructureID}
                            allowedStructures={allowedStructures}
                        />
                    </FormControl>
                    {!!validatorOverrides?.['Tags'] || cdi?.TagsMinMax?.length == 2 ? (
                        <FormControl fullWidth sx={{ my: 1 }}>
                            <TagsSelector
                                required // currently assume required
                                selected={state.Tags || []}
                                onChange={(v) => {
                                    setState({ ...state, Tags: v || null })
                                }}
                                hasError={!!errors.Tags}
                                tagTypes={[state.Type as TagType]}
                            />
                        </FormControl>
                    ) : null}
                </Stack>
            </DialogContent>
            <DialogActions>
                <Stack direction='row' justifyContent='space-between' alignItems='flex-end' gap='32px' width={'100%'}>
                    {!!onImport && <Button onClick={onImport}>Import</Button>}

                    <Button variant='outlined' color='secondary' onClick={onClose} style={{ marginLeft: 'auto' }}>
                        Cancel
                    </Button>
                    <Stack direction={'column'} spacing={2} justifyContent='flex-end' alignItems='flex-end'>
                        <FormControlLabel
                            control={<Switch />}
                            label={'Open in editor'}
                            labelPlacement={'end'}
                            onChange={() => setNavigateToEditor(!navigateToEditor)}
                            checked={navigateToEditor}
                        />
                        <Button
                            fullWidth
                            variant='contained'
                            color='primary'
                            disabled={loading}
                            onClick={() => {
                                if (!validateAll()) {
                                    return
                                }

                                httpPost(`${BASE}/api/v2/content`, state, z.string())
                                    .then((newID) => {
                                        notify(`${state.Type} created: ${state?.Title || ''}, id: ${newID}`, 'info')
                                        setLoading(false)
                                        newID && onCreate(newID, state)
                                        if (navigateToEditor && newID) {
                                            navigateTo(
                                                !!cdi
                                                    ? `${getFeaturedMenuItemContentEditorPath(
                                                          cdi.Route,
                                                          newID,
                                                          'live'
                                                      )}`
                                                    : `/content-editor/${newID}/${state.Workspace}`
                                            )
                                        }
                                        onClose()
                                    })
                                    .catch((e) => {
                                        setLoading(false)
                                        notify(guessErrorMessage(e), 'error')
                                    })
                            }}
                        >
                            Create {state.Type}
                        </Button>
                    </Stack>
                </Stack>
            </DialogActions>
        </Dialog>
    )
}
