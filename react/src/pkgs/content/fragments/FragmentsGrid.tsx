import React, { useEffect, useMemo, useState } from 'react'
import { GridColDef } from '@mui/x-data-grid'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import Button from '@mui/material/Button'
import { useAppContext, useCurrentSite } from '../../auth/atoms'
import { Content, ContentType } from '../types'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import CreateIcon from '@mui/icons-material/Create'
import DeleteIcon from '@mui/icons-material/Delete'
import { FragmentEditor } from './FragmentEditor'
import { StructureSelector } from '../../structure/StructureSelector'
import { AddButton } from '../../../common/components'
import { notify } from '../../../helpers'
import { Preview, RestoreFromTrash } from '@mui/icons-material'
import moment from 'moment'
import { ContentQueryParams, deleteContentQuery, restoreContentQuery, useContentQuery } from '../queries'
import PageContainerWithHeader from '../../../common/components/PageContainerWithHeader'
import SearchBar from '../../../common/components/SearchBar'
import { FormControlLabel, Grid, Switch } from '@mui/material'
import { StatusSelector } from '../BaseForm'
import {
    IDToNameCell,
    MenuLightCell,
    NullableDateCell,
    PublishPeriodCell,
    TwoLinesCell
} from '../../grid/cells/GridCells'
import { DataGridBase } from '../../grid/DataGridBase'
import { BASE } from '@/common/constants'
import { defaultPageQuery } from '@/common/react-query'
import { TagType } from '@/pkgs/system/tags/types'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'

export function FragmentsGrid() {
    const { data: cdi } = useURLParamMenuItem()

    const currentSite = useCurrentSite()
    const appContext = useAppContext()

    const defaultQuery = useMemo(() => {
        return {
            ...defaultPageQuery,
            ContentTypes: [ContentType.Fragment],
            Sites: appContext.getDefaultSitesForSelectors(),
            StructureID: cdi?.Structures?.[0] || null,
            Tags: cdi?.Tags || undefined
        }
    }, [defaultPageQuery, cdi?.Structures, cdi?.Tags])

    useEffect(() => {
        setQuery({ ...query, Sites: appContext.getDefaultSitesForSelectors() })
    }, [currentSite?.ID])

    const [query, setQuery] = useState<ContentQueryParams>(defaultQuery)
    const results = useContentQuery(query)

    const [selectedContent, setSelectedContent] = React.useState<
        | {
              ID: string
              Workspace: string
          }
        | undefined
    >(undefined)
    const [openEditor, setOpenEditor] = React.useState(false)

    useEffect(() => {
        setQuery({ ...query, page: 1 })
    }, [query.Search, query.ContentTypes, query.Sites, query.Tags])

    const deleteContent = (id: string, workspace: string) => {
        if (!confirm('Are you sure you want to delete this fragment?')) return
        deleteContentQuery(id, workspace).then(
            () => results.refetch(),
            (err) => notify(guessErrorMessage(err), 'error')
        )
    }

    const restoreContent = (id: string) => {
        restoreContentQuery(id).then(
            () => results.refetch(),
            (err) => notify(guessErrorMessage(err), 'error')
        )
    }

    const menuItems = (content: Content) => {
        return (onClose: () => void) => {
            return content.Active
                ? [
                      <CustomMenuItem
                          key={'Preview'}
                          onClick={() => {
                              window.open(`${BASE}/api/v1/fragments/${content.ID}/compile`, '_blank')
                              onClose()
                          }}
                          text={'Preview'}
                      >
                          <Preview />
                      </CustomMenuItem>,
                      <CustomMenuItem
                          key={'edit'}
                          onClick={() => {
                              setSelectedContent({ ID: content.ID, Workspace: content.Workspace })
                              setOpenEditor(true)
                              onClose()
                          }}
                          text={'Edit'}
                      >
                          <CreateIcon />
                      </CustomMenuItem>,
                      <CustomMenuItem
                          key={'delete'}
                          onClick={() => {
                              deleteContent(content.ID, content.Workspace)
                              onClose()
                          }}
                          text={'Delete'}
                      >
                          <DeleteIcon />
                      </CustomMenuItem>
                  ]
                : [
                      <CustomMenuItem
                          key={'delete'}
                          onClick={() => {
                              restoreContent(content.ID)
                              onClose()
                          }}
                          text={'Restore'}
                      >
                          <RestoreFromTrash />
                      </CustomMenuItem>
                  ]
        }
    }

    const columns: GridColDef[] = [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 2,
            filterable: false,
            sortable: true,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'Updated',
            headerName: 'Updated',
            width: 200,
            filterable: false,
            sortable: true,
            renderCell: (params) => {
                const date = moment(params.row.Updated || params.row.Created)
                    .format('YYYY-MM-DD, h:mm:ss a')
                    ?.split(',')
                return <TwoLinesCell l1={date[0]} l2={date[1]} />
            }
        },
        {
            field: 'StructureID',
            headerName: 'Structure',
            width: 300,
            filterable: false,
            sortable: false,
            renderCell: (params) => <IDToNameCell tableName={'structure'} ID={params.row.StructureID} />
        },
        {
            field: 'PublishPeriod',
            headerName: 'Status',
            width: 250,
            sortable: false,
            filterable: false,
            renderCell: (params) => (
                <PublishPeriodCell
                    publishAt={params.row.PublishAt}
                    expireAt={params.row.ExpireAt}
                    workspace={params.row.Workspace}
                />
            )
        },
        {
            field: 'publish_at',
            headerName: 'Publish At',
            flex: 1,
            sortable: true,
            filterable: false,
            renderCell: (params) => <NullableDateCell row={params.row} fieldName={'PublishAt'} />
        },
        {
            field: 'expire_at',
            headerName: 'Expire At',
            flex: 1,
            sortable: true,
            filterable: false,
            renderCell: (params) => <NullableDateCell row={params.row} fieldName={'ExpireAt'} />
        },
        {
            field: 'Menu',
            headerName: '...',
            width: 80,
            sortable: false,
            filterable: false,
            renderCell: (params) => <MenuLightCell itemsFactory={menuItems(params.row)} />
        }
    ]

    return (
        <PageContainerWithHeader
            title={cdi?.Label || 'Fragments'}
            topRightElement={
                <AddButton
                    title={cdi?.Label ? 'Create' : `Add Fragment`}
                    func={() => {
                        setSelectedContent(undefined)
                        setOpenEditor(true)
                    }}
                />
            }
        >
            <Grid container spacing={2}>
                <Grid item xs={4}>
                    <SearchBar value={query.Search || ''} onChange={(val) => setQuery({ ...query, Search: val })} />
                </Grid>
                <Grid item xs={4}>
                    <StructureSelector
                        allowedStructures={cdi?.Structures}
                        value={query.StructureID}
                        onChange={(v) => setQuery({ ...query, StructureID: v })}
                    />
                </Grid>
                <Grid item xs={4}>
                    <TagsSelector
                        allowedTags={cdi?.Tags || undefined}
                        selected={query.Tags || []}
                        onChange={(tags) => setQuery((p) => ({ ...p, Tags: tags }))}
                        tagTypes={[TagType.Fragment]}
                    />
                </Grid>
                <Grid item xs={4}>
                    <StatusSelector value={query.Status} onChange={(v) => setQuery({ ...query, Status: v, page: 1 })} />
                </Grid>
                <Grid item xs={2}>
                    <FormControlLabel
                        value='start'
                        control={
                            <Switch
                                checked={query.SiteOnly || false}
                                onChange={(e) =>
                                    setQuery({ ...query, SiteOnly: e.target.checked, Status: '', page: 1 })
                                }
                                color='secondary'
                            />
                        }
                        label='Site Only'
                        labelPlacement='start'
                    />
                </Grid>
                <Grid item xs={2}>
                    <FormControlLabel
                        value='start'
                        control={
                            <Switch
                                checked={query.Inactive || false}
                                onChange={(e) =>
                                    setQuery({ ...query, Inactive: e.target.checked, Status: '', page: 1 })
                                }
                                color='secondary'
                            />
                        }
                        label='Deleted'
                        labelPlacement='start'
                    />
                </Grid>
                <Grid item xs={4}>
                    <Button
                        style={{ marginTop: '0.25rem', float: 'right' }}
                        onClick={() => {
                            setQuery(defaultQuery)
                        }}
                    >
                        Reset filters
                    </Button>
                </Grid>
                <Grid item xs={12}>
                    {results.isLoading && <div>Loading...</div>}
                    {results.error && <div>Error: {guessErrorMessage(results.error)}</div>}
                    {results.data && (
                        <DataGridBase
                            // rowHeight={100}
                            columns={columns}
                            state={results.data}
                            setQuery={setQuery}
                        />
                    )}
                </Grid>
            </Grid>
            {openEditor && (
                <FragmentEditor
                    id={selectedContent?.ID}
                    workspace={selectedContent?.Workspace || 'live'}
                    open={openEditor}
                    onClose={() => {
                        setOpenEditor(false)
                        results.refetch()
                    }}
                    onSuccessfulSave={() => results.refetch()}
                />
            )}
        </PageContainerWithHeader>
    )
}
