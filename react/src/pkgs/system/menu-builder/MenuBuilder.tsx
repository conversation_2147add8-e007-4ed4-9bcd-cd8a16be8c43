import PageContainerWithHeader from '@/common/components/PageContainerWithHeader'
import { useMemo, useState } from 'react'
import { MenuBuilderForm, MenuBuilderFormData, MenuBuilderFormDataErrors } from './MenuBuilderForm'
import { AddButton, ConfirmAction } from '@/common/components'
import { useMutation } from '@tanstack/react-query'
import { ContentType } from '@/pkgs/content/types'
import { Button, DialogActions, DialogContent, FormHelperText, Stack, Typography } from '@mui/material'
import CMDialog from '@/common/components/CMDialog'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { _isEqual, notify } from '@/helpers'
import { CustomMenuItemType, customMenuItemTypes, getSettingsDTO, ICustomMenuItem, useMenuBuilder } from './queries'
import { v4 } from 'uuid'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { CustomMenuItemsSortableList } from './CustomMenuItemsSortableList'
import { StandardList } from './StandardList'
import SidebarContainer from '@/common/components/SidebarContainer'

function upsertCustomMenuItem(existingItems: ICustomMenuItem[], newItem: ICustomMenuItem) {
    if (!existingItems) return [newItem]
    if (existingItems.find((item) => item.ID != newItem.ID && item.Route == newItem.Route)) {
        notify('Another menu item with the same route exists. Please change the route.', 'error')
        throw Error('Another menu item with the same route exists. Please change the route.')
    }

    for (let i = 0; i < existingItems.length; i++) {
        if (existingItems[i].ID == newItem.ID) {
            existingItems[i] = newItem
            return existingItems
        }
    }

    return [...existingItems, newItem]
}

function deleteCustomMenuItem(existingItems: ICustomMenuItem[], id: string) {
    for (let i = 0; i < existingItems.length; i++) {
        if (existingItems[i].ID == id) {
            existingItems.splice(i, 1)
            return existingItems
        }
    }

    throw Error('Record not found')
}

const validators: Partial<Record<keyof MenuBuilderFormData, (i: MenuBuilderFormData) => string | undefined>> = {
    ContentType: (item: MenuBuilderFormData) => {
        if (!customMenuItemTypes.includes(item.ContentType as CustomMenuItemType)) {
            return 'Content type must be one of Page, News, Event, Fragment'
        }
    },
    Route: (item: MenuBuilderFormData) => {
        if (item?.Route.replace('/', '')?.length < 3) {
            return 'Route must be at least 3 characters'
        }

        if (item.Route[0] != '/') {
            return 'Route must start with a forward slash: /'
        }
    },
    Label: (item: MenuBuilderFormData) => {
        if (item?.Label?.length < 1) {
            return 'Label cannot be empty'
        }

        if (item.Label.length > 50) {
            return 'Label can have a maximum of 50 characters'
        }
    },
    TagsMinMax: (item: MenuBuilderFormData) => {
        if (item?.TagsMinMax?.length > 2) {
            return 'TagsMinMax can only contain 2 numbers: [min, max]'
        }
    }
}

function validateMenuBuilderFormData(item: MenuBuilderFormData): MenuBuilderFormDataErrors {
    const errors: MenuBuilderFormDataErrors = {}

    for (const key in validators) {
        const error = validators[key]?.(item)
        if (!!error) {
            errors[key] = error
        }
    }

    return errors
}

function getDefaultMenuBuilderFormData(): MenuBuilderFormData {
    return {
        ID: v4(),
        Route: '',
        Label: '',
        ContentType: ContentType.Page,
        TagsMinMax: [0, 999],
        Active: true,
        IconName: 'CreateIcon'
    }
}

// Contains both StandardMenuItems UI and CustomMenuItems UI
// Contains only logic for CustomMenuItemsUI
// All the logic for StandardMenuItems is contained in <StandardList />
export function MenuBuilder() {
    const menuBuilder = useMenuBuilder()
    const CustomMenuItems = menuBuilder?.menuItems?.CustomMenuItems || []
    const StandardMenuItems = menuBuilder?.menuItems?.StandardMenuItems || []

    const [menuBuilderFormMode, setMenuBuilderFormMode] = useState<'Create' | 'Edit'>('Create')

    const [formData, setFormData] = useState<MenuBuilderFormData>(getDefaultMenuBuilderFormData())
    const [formDataErrors, setFormDataErrors] = useState<MenuBuilderFormDataErrors>({})
    const [onSubmitError, setOnSubmitError] = useState('')

    const [formDialogIsOpen, setFormDialogIsOpen] = useState(false)
    const [confirmDeleteDialogIsOpen, setConfirmDeleteDialogIsOpen] = useState(false)

    const originalFormData = useMemo(() => {
        return CustomMenuItems?.find((cdi) => cdi.ID == (formData as ICustomMenuItem)?.ID)
    }, [menuBuilder.data?.Data, formData])

    const hasChanges = useMemo(() => {
        return !_isEqual(formData, originalFormData)
    }, [formData, originalFormData])

    const updateCustomMenuItems = useMutation({
        mutationFn: (customMenuItems: ICustomMenuItem[]) =>
            menuBuilder.upsertSettings(getSettingsDTO(customMenuItems, StandardMenuItems)),
        onSuccess: (data) => {
            notify('Success!', 'success')
            setFormDialogIsOpen(false)
            menuBuilder.refetch()
        },
        onError: (err) => {
            const errorMsg = guessErrorMessage(err)
            setOnSubmitError(errorMsg)
        }
    })

    if (menuBuilder.isLoading) {
        return <CenteredSpinner />
    }

    return (
        <PageContainerWithHeader title='Menu Builder'>
            <SidebarContainer
                title=''
                tabs={[
                    {
                        name: 'Custom Menu List',
                        content: CustomMenuItems && (
                            <>
                                <Stack direction='row' gap='0.8rem' justifyContent='space-between' mb='1.2rem'>
                                    <Typography variant='subtitle1'>
                                        Configure Custom Menu Items To Link To Specific Content:
                                    </Typography>
                                    <AddButton
                                        title={`add custom menu item`}
                                        func={() => {
                                            setMenuBuilderFormMode('Create')
                                            setFormData(getDefaultMenuBuilderFormData())
                                            setFormDialogIsOpen(true)
                                        }}
                                    />
                                </Stack>
                                <CustomMenuItemsSortableList
                                    items={CustomMenuItems}
                                    onChange={(items) => {
                                        updateCustomMenuItems.mutate(items)
                                    }}
                                    editItemHandler={(item) => {
                                        setMenuBuilderFormMode('Edit')
                                        setFormData(item)
                                        setFormDialogIsOpen(true)
                                    }}
                                    deleteItemHandler={(item) => {
                                        setFormData(item)
                                        setConfirmDeleteDialogIsOpen(true)
                                    }}
                                />
                            </>
                        )
                    },
                    {
                        name: 'Standard Menu List',
                        content: <StandardList />
                    }
                ]}
            />

            <ConfirmAction
                title='Are you sure?'
                text={`Deleting ${formData.Label}`}
                open={confirmDeleteDialogIsOpen}
                handleAgree={() => {
                    if ('ID' in formData) {
                        updateCustomMenuItems.mutate(
                            deleteCustomMenuItem(CustomMenuItems, (formData as ICustomMenuItem).ID)
                        )
                        setConfirmDeleteDialogIsOpen(false)
                    }
                }}
                handleClose={() => {
                    setConfirmDeleteDialogIsOpen(false)
                }}
                handleDisagree={() => {
                    setConfirmDeleteDialogIsOpen(false)
                }}
            />
            <CMDialog
                open={formDialogIsOpen}
                showCloseButton
                onClose={() => {
                    setFormDialogIsOpen(false)
                }}
                title={`${menuBuilderFormMode} Featured Menu Item`}
                maxWidth='lg'
            >
                <DialogContent sx={{ padding: 0 }}>
                    <MenuBuilderForm
                        formData={formData}
                        formDataErrors={formDataErrors}
                        onChange={(menuItem) => {
                            setFormData(menuItem)
                        }}
                        isEditMode={menuBuilderFormMode == 'Edit'}
                    />
                </DialogContent>
                <DialogActions>
                    <Stack direction='column' justifyContent='flex-end'>
                        <Stack direction='row' sx={{ gap: '8px', justifyContent: 'flex-end' }}>
                            <Button
                                disabled={!hasChanges}
                                variant='text'
                                color='primary'
                                type='reset'
                                onClick={() => {
                                    if (menuBuilderFormMode == 'Edit') {
                                        if (originalFormData) {
                                            setFormData(originalFormData)
                                        }

                                        return
                                    }
                                    setFormData(getDefaultMenuBuilderFormData())
                                    setFormDataErrors({})
                                    setOnSubmitError('')
                                }}
                            >
                                Reset Changes
                            </Button>
                            <Button
                                disabled={!hasChanges}
                                variant='contained'
                                color='primary'
                                type='submit'
                                onClick={() => {
                                    const errors = validateMenuBuilderFormData(formData)
                                    setFormDataErrors(errors)
                                    console.log('errors', errors)
                                    if (Object.values(errors).length == 0) {
                                        updateCustomMenuItems.mutate(upsertCustomMenuItem(CustomMenuItems, formData))
                                    }
                                }}
                            >
                                Save
                            </Button>
                        </Stack>
                        {onSubmitError && (
                            <FormHelperText error={true}>
                                <Typography>{onSubmitError}</Typography>
                            </FormHelperText>
                        )}
                    </Stack>
                </DialogActions>
            </CMDialog>
        </PageContainerWithHeader>
    )
}
