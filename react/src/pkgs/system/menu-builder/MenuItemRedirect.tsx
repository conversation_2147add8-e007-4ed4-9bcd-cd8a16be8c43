import { ContentType } from '@/pkgs/content/types'
import { FragmentsGrid } from '@/pkgs/content/fragments/FragmentsGrid'
import { useURLParamMenuItem } from './useURLParamMenuItem'
import { ContentGrid } from '@/pkgs/content/grids/ContentGrid'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { Redirect } from '@/app/Redirect'
import { useMenuItemsQuery } from './queries'

interface MenuItemRedirectProps {
    // cdi: CustomDrawerItem
}

// Handles redirecting user from custom menu item created by Menu Builder
export function MenuItemRedirect({}: MenuItemRedirectProps) {
    const results = useMenuItemsQuery()
    const { data, isLoading, error } = useURLParamMenuItem()

    if (isLoading) return <CenteredSpinner />

    // TODO: Error page
    if (!data || error) return <></>

    if (!data.Active) return <Redirect to={'/'} />

    if (!results.menuItems.CustomMenuItems.find((cdi) => cdi.Route == data.Route)) {
        // unauthorized?
        return <Redirect to={'/'} />
    }

    if (data.ContentType == ContentType.Fragment) {
        return <FragmentsGrid key={data.Route} />
    }

    return <ContentGrid key={data.Route} ContentTypes={[data.ContentType as ContentType]} />
}
