import React, {
    HTMLAttributeAnchorTarget,
    ReactElement,
    ReactNode,
    useCallback,
    useEffect,
    useMemo,
    useState
} from 'react'
import { makeStyles } from '@mui/styles'
import { Box, Icon, IconButton, <PERSON>per, SxProps } from '@mui/material'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import LabelOutlinedIcon from '@mui/icons-material/LabelOutlined'
import { HelpTooltip } from '../../../common/components'
import { Get } from '../../../helpers'
import moment from 'moment'
import AccountTreeOutlinedIcon from '@mui/icons-material/AccountTreeOutlined'
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import {
    formatAllDayEndDateTimestamp,
    formatAllDayStartDateTimestamp,
    formatRegularTimestamp
} from '../../../helpers/timestamp'
import {
    getContentEditorPath,
    getFeaturedMenuItemContentEditorPath,
    useAppNavigation
} from '../../../app/useAppNavigation'
import { httpGet, nameZ$ } from '../../../common/client'
import { getPublishStatus, publishStatusColour } from '../../content/editor/ContentEditorSaveBar'
import { baseQueryConfig, Trackable } from '../../../common/react-query'
import { useAppContext } from '@/pkgs/auth/atoms'
import CMLink from '@/common/CMLink'
import EventRepeatIcon from '@mui/icons-material/EventRepeat'
import { useQuery } from '@tanstack/react-query'
import { EditorsAPI } from '@/common/constants'
import { z } from 'zod'
import { Editor } from '../selectors/EditorsSelectorV2'
import { useTagsQueryV2 } from '@/pkgs/system/tags/queries'
import { useURLParamMenuItem } from '@/pkgs/system/menu-builder/useURLParamMenuItem'

const uuidNil = '********-0000-0000-0000-************'

export * from './MenuLightCell'

export const TitleCell = ({ row }) => {
    const { data: cdi } = useURLParamMenuItem()
    const { data: tagsData, isLoading } = useTagsQueryV2({
        Search: '',
        pageSize: 1000
    })

    const appContext = useAppContext()
    appContext

    const tagIdToTagNameMap = useMemo(
        () =>
            tagsData?.Rows?.reduce(
                (a, t) => ({
                    ...a,
                    [t.ID]: t.Name
                }),
                {} as Record<string, string>
            ) || null,
        [tagsData?.Rows]
    )
    const { getRelativePath } = useAppNavigation()

    const rowId = row?.id || row?.ID
    const title = row?.title || row?.Title
    const tagNames =
        row?.tags?.map((t) => t.name) ||
        row?.Tags?.map((tagId) => tagIdToTagNameMap?.[tagId] || '').filter((name) => !!name)
    const settings = row?.settings || row?.Settings
    const navParents = row?.navParents
    let parents = []
    if (navParents) parents = navParents.filter(({ id }) => id !== rowId && id !== uuidNil)

    const editorPath = getRelativePath(
        cdi?.Route ? getFeaturedMenuItemContentEditorPath(cdi?.Route || '', row.ID, row.Workspace) : getContentEditorPath(row) || ''
    )

    return (
        <CellWrapper>
            {navParents && navParents.length > 0 && (
                <CellLine>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        {parents && parents.length > 0 && (
                            <AccountTreeOutlinedIcon
                                style={{ fontSize: '1rem', opacity: 0.5, marginRight: '0.2rem' }}
                            />
                        )}
                        <span
                            style={{
                                lineHeight: 1.8,
                                opacity: 0.5
                            }}
                        >
                            {parents.map(({ title }) => title).join(' > ')}
                        </span>
                    </div>
                </CellLine>
            )}
            <CellLine href={editorPath || undefined}>
                {settings?.rrule && (
                    <Icon
                        component={EventRepeatIcon}
                        sx={{
                            mr: 1,
                            verticalAlign: 'middle',
                            width: '1.2rem',
                            height: '1.2rem'
                        }}
                    />
                )}
                <span style={{ fontSize: 18 }}>{title}</span>
            </CellLine>
            {tagNames?.length > 0 && (
                <CellLine>
                    <div
                        style={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            flexWrap: 'wrap',
                            lineHeight: 1.8,
                            opacity: 0.5
                        }}
                    >
                        <LabelOutlinedIcon style={{ fontSize: 15 }} />
                        <span>&nbsp;{tagNames.join(', ')}</span>
                    </div>
                </CellLine>
            )}
        </CellWrapper>
    )
}

export const TemplateCell = ({ row }) => {
    const { templateTitle } = row
    return (
        <CellWrapper>
            <CellLine>{templateTitle}</CellLine>
        </CellWrapper>
    )
}

export const PrivacyLevelCell = ({ row }) => {
    const privacyLevel = row?.privacyLevel !== undefined ? row?.privacyLevel : row?.PrivacyLevel
    let text = 'undefined'
    switch (privacyLevel) {
        case 2:
            text = 'staff'
            break
        case 0:
            text = 'public'
            break
    }
    return (
        <CellWrapper>
            <CellLine>{text}</CellLine>
        </CellWrapper>
    )
}

export const EditorsCellV2 = ({ row }) => {
    const results = useQuery({
        ...baseQueryConfig,
        queryKey: ['editors-query-key'],
        queryFn: async () => httpGet(EditorsAPI, null, z.any()),
        enabled: false
    })

    const editorIdToEditor = useMemo(
        () =>
            results?.data?.reduce(
                (a, t) => ({
                    ...a,
                    [t.id]: t
                }),
                {} as Record<string, Editor>
            ),
        [results?.data]
    )

    return (
        <CellWrapper>
            <CellLine>{editorIdToEditor?.[row.Owner]?.name}</CellLine>
            <CellLine>{editorIdToEditor?.[row.Publisher]?.name}</CellLine>
        </CellWrapper>
    )
}
export const EditorsCell = ({ row }) => {
    const { ownerName, publisherName, owner, publisher } = row
    return (
        <CellWrapper>
            <CellLine>{ownerName ? ownerName.Firstname + ' ' + ownerName.Lastname : owner}</CellLine>
            <CellLine>{publisherName ? publisherName.Firstname + ' ' + publisherName.Lastname : publisher}</CellLine>
        </CellWrapper>
    )
}

export const DateCell = ({ row }) => {
    const created = Date.parse(row['created'] || row['CreatedAt'] || row['Created'])
    const updated = Date.parse(row['updated'] || row['UpdatedAt'] || row['Updated'])
    // TODO => Mismatch here
    const label = created === updated ? 'Created' : 'Modified'
    const value = moment(updated).format('YYYY-MM-DD, h:mm:ss a')?.split(',')
    const date = value?.[0]
    const time = value?.[1]
    return (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem' }}>
            <div style={{ lineHeight: 'normal' }}>
                <strong>{label}</strong>
            </div>
            <div style={{ lineHeight: 'normal' }}>
                {date}
                <br />
                {time}
            </div>
        </div>
    )
}

function SiteName({ siteId }: { siteId: string }) {
    const name = useIDToName({ tableName: 'site', ID: siteId })
    return <>{name}</>
}

export function SitesCell({ sites, displayCount = 1 }: { sites: string[] | undefined; displayCount?: number }) {
    return (
        <Box sx={{ whiteSpace: 'normal' }}>
            {sites && sites.length > displayCount ? (
                <>
                    {sites.slice(0, displayCount).map((site) => (
                        <span key={site}>
                            <SiteName siteId={site} />,{' '}
                        </span>
                    ))}
                    ... + {sites?.length - 1} More
                </>
            ) : sites?.length == 1 ? (
                <SiteName siteId={sites[0]} />
            ) : (
                'District Wide'
            )}
        </Box>
    )
}

export const NullableDateCell = ({ row, fieldName }) => {
    const updated = Date.parse(row[fieldName])
    // TODO => Mismatch here
    const value = updated ? moment(updated).format('YYYY-MM-DD, h:mm:ss a') : 'not set'
    const [date, time] = value.split(',')

    return (
        <div style={{ lineHeight: 'normal' }}>
            <div style={{ lineHeight: 'normal' }}>
                {date}
                {time ? <br /> : ''}
                {time}
            </div>
        </div>
    )
}

// for news only
export const SettingsDatesCell = ({ row, settingsName }) => {
    const value = moment(row?.['settings']?.[settingsName])
        .format('YYYY-MM-DD, h:mm:ss a')
        ?.split(',')
    const date = value?.[0]
    const time = value?.[1]
    return date && time ? (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem' }}>
            <div style={{ lineHeight: 'normal' }}>
                {date}
                <br></br>
                {time}
            </div>
        </div>
    ) : (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem' }}>
            <div style={{ lineHeight: 'normal' }}>&nbsp;</div>
        </div>
    )
}

// for events only
// if isAllDay is true, set end date to the previous day
// EventEdtior.js for more info
export const EventsDatesCell = ({ row, settingsName }) => {
    const isAllDay = row?.settings?.isAllDay || false
    const timestamp = row?.settings?.[settingsName]
    const fmtTimestamp = isAllDay
        ? settingsName === 'enddate'
            ? formatAllDayEndDateTimestamp(timestamp)
            : formatAllDayStartDateTimestamp(timestamp)
        : formatRegularTimestamp(timestamp)

    const [date, time] = fmtTimestamp.split(',')
    return date || time ? (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem' }}>
            <div style={{ lineHeight: 'normal' }}>
                {date}
                {isAllDay && settingsName == 'enddate' ? '*' : ''}
                <br></br>
                {time}
            </div>
        </div>
    ) : (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem' }}>
            <div style={{ lineHeight: 'normal' }}>&nbsp;</div>
        </div>
    )
}

export const NotesCell = ({ row, style }) => {
    const state = row
    const status = getPublishStatus(state.publish_at, state.expire_at)
    const statusMap = {
        draft: 'edit_note',
        published: 'done',
        scheduled: 'schedule_send',
        expired: 'auto_delete'
    }
    return (
        <div style={{ lineHeight: 'normal', ...style }}>
            <HelpTooltip
                customIcon={
                    <span
                        className='material-icons-outlined'
                        style={{ fontSize: '1.5rem', color: '#6c6c6c', cursor: 'help' }}
                    >
                        {statusMap[status]}
                    </span>
                }
                contents={[status]}
                position={{ width: '10rem', left: 0 }}
                removeDecoration
            />
            {Get.isDepartment(state) && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.3rem', color: '#4E01D5', cursor: 'help' }}
                        >
                            corporate_fare
                        </span>
                    }
                    contents={[`This page belongs to ${state?.department?.name}`]}
                    position={{ width: '10rem', left: 0, whiteSpace: 'normal' }}
                    removeDecoration
                />
            )}
            {state.sites.length === 0 && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.4rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            person_off
                        </span>
                    }
                    contents={['This page is not shared with any sites']}
                    position={{ width: '10rem', left: 0, whiteSpace: 'normal' }}
                    removeDecoration
                />
            )}
            {state.sites.length > 1 && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.4rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            group
                        </span>
                    }
                    contents={[
                        'This page is shared with more than one site and only editable with the right Permissions'
                    ]}
                    position={{ width: '10rem', left: 0, whiteSpace: 'normal' }}
                    removeDecoration
                />
            )}

            {Get.isElapsed(state, 'expirationDate') && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.3rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            timer_off
                        </span>
                    }
                    contents={['This page is expired/unpublished']}
                    position={{ width: '10rem', left: 0 }}
                    removeDecoration
                />
            )}
            {Get.isDistrict(state) && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.3rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            hotel_class
                        </span>
                    }
                    contents={[
                        'This page is shared with more than one site and includes a portion of content which is specific to this site'
                    ]}
                    position={{ width: '10rem', left: 0, whiteSpace: 'normal' }}
                    removeDecoration
                />
            )}
            {state?.settings?.imported && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.5rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            transit_enterexit
                        </span>
                    }
                    contents={[
                        `This ${state?.type === 'news' ? 'article' : 'event'} is imported ${
                            state?.settings?.importInfo?.source ? `from ${state.settings.importInfo.source}` : ''
                        }`,
                        'Changes made to imported content may not persist through the import process'
                    ]}
                    position={{ width: '10rem', left: 0 }}
                    removeDecoration
                />
            )}
            {!!Get.migrationInfo(state) && (
                <HelpTooltip
                    customIcon={
                        <span
                            className='material-icons-outlined'
                            style={{ fontSize: '1.5rem', color: '#6c6c6c', cursor: 'help' }}
                        >
                            move_down
                        </span>
                    }
                    contents={[`Content created by Automated Migration`]}
                    position={{ width: '10rem', left: 0 }}
                    removeDecoration
                />
            )}
        </div>
    )
}

export const CellWrapper = (props) => {
    return <div style={{ lineHeight: 'normal', width: '100%', ...props.style }}>{props.children}</div>
}

const useStylesCellLine = makeStyles((theme) => ({
    paper: {
        border: '1px solid',
        padding: theme.spacing(1),
        backgroundColor: theme.palette.background.paper
    }
}))

export const CellLine = (props) => {
    const classes = useStylesCellLine()
    const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null)

    function isEllipsisActive(e) {
        return e.offsetWidth < e.scrollWidth
    }

    const Wrapper = useCallback(
        ({ children }) => {
            return props?.href ? (
                <CMLink
                    to={props.href}
                    target={props.target}
                    sx={{
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                        width: '100%',
                        display: 'block'
                    }}
                    onMouseEnter={(event) =>
                        setAnchorEl(isEllipsisActive(event.currentTarget) ? event.currentTarget : null)
                    }
                    onMouseLeave={() => setAnchorEl(null)}
                >
                    {children}
                </CMLink>
            ) : (
                <div
                    style={{
                        lineHeight: 'normal',
                        width: '100%',
                        textOverflow: 'ellipsis',
                        display: 'block',
                        overflow: 'hidden'
                    }}
                    onMouseEnter={(event) =>
                        setAnchorEl(isEllipsisActive(event.currentTarget) ? event.currentTarget : null)
                    }
                    onMouseLeave={() => setAnchorEl(null)}
                >
                    {children}
                </div>
            )
        },
        [props?.href]
    )

    return (
        <Wrapper>
            {props.children}
            {/*@ts-ignore*/}
            <Popper open={!!anchorEl} anchorEl={anchorEl} placement='right-end'>
                <div className={classes.paper}>{props.children}</div>
            </Popper>
        </Wrapper>
    )
}

export const CellMenu = ({ row, setMenu }) => {
    return (
        setMenu && (
            <div className={'site-options icon'}>
                <IconButton
                    aria-controls={'pageMenu'}
                    aria-haspopup='true'
                    onClick={(e) => setMenu(e, row)}
                    size='large'
                >
                    <MoreHorizIcon />
                </IconButton>
            </div>
        )
    )
}
export const ReorderMenu = ({ row, pinnedNews, handleReorder }) => {
    const index = pinnedNews.indexOf(row)
    const disabled = row.disabled

    const lookBack = (index) => {
        if (!pinnedNews?.[index]) {
            return -1
        }
        for (let i = index; i > -1; i--) {
            if (pinnedNews?.[i]?.disabled === false) {
                return i
            }
        }
        return -1
    }
    const lookAhead = (index) => {
        if (!pinnedNews?.[index]) {
            return -1
        }
        for (let i = index; i < pinnedNews.length; i++) {
            if (pinnedNews?.[i]?.disabled === false) {
                return i
            }
        }
        return -1
    }

    const swap = (newIndex) => {
        const clone = [...pinnedNews]
        clone[index] = pinnedNews[newIndex]
        clone[newIndex] = row
        for (let i = 0; i < clone.length; i++) clone[i].settings.priority = i
        handleReorder(clone)
    }
    const moveDown = () => {
        const downIndex = lookAhead(index + 1)
        if (downIndex === -1) {
            return
        }
        swap(downIndex)
    }
    const moveUp = () => {
        const upIndex = lookBack(index - 1)
        if (upIndex === -1) {
            return
        }
        swap(upIndex)
    }

    return (
        <div className='column-seven flex-column'>
            <ArrowDropUpIcon
                onClick={moveUp}
                style={{
                    cursor: disabled ? 'default' : 'pointer',
                    opacity: disabled ? '0.3' : '1'
                }}
            />
            <ArrowDropDownIcon
                onClick={moveDown}
                style={{
                    cursor: disabled ? 'default' : 'pointer',
                    opacity: disabled ? '0.3' : '1'
                }}
            />
        </div>
    )
}

export const AdminCell = ({ ID }: { ID: string }) => {
    const [name, setName] = useState<string>('...')
    useEffect(() => {
        const subs = nameZ$('account', ID).subscribe((res) => {
            if (res && res.ID === ID) {
                setName(res.Name)
                subs.unsubscribe()
            }
        })
        return () => {
            subs.unsubscribe()
        }
    }, [])

    return (
        <div className={'MuiDataGrid-cellContent'}>
            <div className={'MuiDataGrid-cellContent'} title={name}>
                {name}
            </div>
            <div className={'MuiDataGrid-cellContent'} title={ID} style={{ fontSize: '0.7rem', color: '#7e7e7e' }}>
                {ID}
            </div>
        </div>
    )
}

export const TrackableCell = ({ trackable, ownerID }: { trackable: Trackable; ownerID?: string }) => {
    const [name, setName] = useState<string>(trackable.UpdatedBy)

    const created = trackable.CreatedAt === trackable.UpdatedAt
    const prefix = created ? 'Created' : 'Updated'
    const adminID = created ? trackable.CreatedBy : trackable.UpdatedBy

    useEffect(() => {
        if (adminID === '********-0000-0000-0000-************' || adminID === '45f06f48-a93c-414e-b9a0-7582e0abc085') {
            setName('System')
            return () => {}
        }

        const subs = nameZ$('account', adminID).subscribe((res) => {
            if (res && res.ID === adminID) {
                setName(res.Name)
                subs.unsubscribe()
            }
        })
        return () => {
            subs.unsubscribe()
        }
    }, [])

    const dateStr = moment(trackable.UpdatedAt).format('YYYY-MM-DD HH:mm:ss')
    const dateStrHuman = moment(trackable.UpdatedAt).fromNow()

    return (
        <div className={'MuiDataGrid-cellContent'}>
            <div className={'MuiDataGrid-cellContent'} title={dateStr}>
                {dateStrHuman}
            </div>
            <div className={'MuiDataGrid-cellContent'} title={adminID} style={{ fontSize: '0.7rem', color: '#7e7e7e' }}>
                {`${prefix} by ${name} [${adminID}]`}
            </div>
        </div>
    )
}

export const AdminOrOwnerCell = ({
    adminID,
    ownerID,
    ownerName
}: {
    adminID: string
    ownerID: string
    ownerName: string
}) => {
    const [name, setName] = useState<string>('...')

    useEffect(() => {
        if (adminID === ownerID || adminID === '********-0000-0000-0000-************') {
            setName(ownerName)
            return () => {}
        }

        const subs = nameZ$('account', adminID).subscribe((res) => {
            if (res && res.ID === adminID) {
                setName(res.Name)
                subs.unsubscribe()
            }
        })
        return () => {
            subs.unsubscribe()
        }
    }, [])

    return (
        <div className={'MuiDataGrid-cellContent'}>
            <div className={'MuiDataGrid-cellContent'} title={name}>
                {name}
            </div>
            <div className={'MuiDataGrid-cellContent'} title={adminID} style={{ fontSize: '0.7rem', color: '#7e7e7e' }}>
                {adminID}
            </div>
        </div>
    )
}

interface IDToNameProps {
    tableName: string
    ID: string | null | undefined
}

export function useIDToName({ tableName, ID }: IDToNameProps) {
    const [name, setName] = useState<string>(ID ? ID : '')
    const appContext = useAppContext()

    useEffect(() => {
        if (!ID) return () => {}

        // all tenant sites are already loaded, so we can use the context
        if (tableName === 'site') {
            const siteName = appContext.getSiteName(ID)
            if (siteName) {
                setName(siteName)
                return () => {}
            }
        }

        const subs = nameZ$(tableName, ID).subscribe((res) => {
            if (res && res.ID === ID) {
                setName(res.Name)
                subs.unsubscribe()
            }
        })
        return () => {
            subs.unsubscribe()
        }
    }, [ID])

    return name
}

export const IDToNameCell = ({ tableName, ID }: IDToNameProps) => {
    const name = useIDToName({ tableName, ID })

    return (
        <div className={'MuiDataGrid-cellContent'}>
            <div className={'MuiDataGrid-cellContent'} title={name}>
                {name || '-'}
            </div>
            <div
                className={'MuiDataGrid-cellContent'}
                title={ID || ''}
                style={{ fontSize: '0.7rem', color: '#7e7e7e' }}
            >
                {ID}
            </div>
        </div>
    )
}

export const TwoLinesCell = ({
    l1,
    l2,
    path,
    target,
    sx
}: {
    l1: ReactNode
    l2?: ReactNode
    path?: string
    target?: HTMLAttributeAnchorTarget
    sx?: SxProps
}) => {
    const Wrapper = useCallback(
        ({ children }) => {
            return path ? (
                <CMLink
                    to={path}
                    target={target}
                    sx={{
                        ...sx,
                        textOverflow: 'ellipsis',
                        overflow: 'hidden',
                        width: '100%',
                        display: 'block'
                    }}
                >
                    {children}
                </CMLink>
            ) : (
                <Box className={'MuiDataGrid-cellContent'} sx={sx}>
                    {children}
                </Box>
            )
        },
        [path]
    )

    return (
        <Wrapper>
            <CellLine>
                <div className={'MuiDataGrid-cellContent'} title={typeof l1 == 'string' ? l1 : undefined}>
                    {l1 || '-'}
                </div>
            </CellLine>
            <div
                className={'MuiDataGrid-cellContent'}
                title={typeof l2 == 'string' ? l2 : undefined}
                style={{ fontSize: '0.7rem', color: '#7e7e7e' }}
            >
                {l2}
            </div>
        </Wrapper>
    )
}

export function PublishPeriodCell({
    publishAt,
    expireAt,
    workspace = 'live'
}: {
    publishAt: string | Date | null | undefined
    expireAt: string | Date | null | undefined
    workspace?: string
}) {
    const status = getPublishStatus(publishAt, expireAt)
    return (
        <div style={{ lineHeight: 'normal', fontSize: '0.75rem', width: '100%' }}>
            <div style={{ lineHeight: 'normal', display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                <strong style={{ color: publishStatusColour[status] }}>{status}</strong>
                <strong>{workspace}</strong>
            </div>
            <div style={{ lineHeight: 'normal' }}>
                <strong>show:</strong> {publishAt ? moment(publishAt).format('YYYY-MM-DD, h:mm:ss a') : 'never'}
                <br />
                <strong>hide:</strong> {expireAt ? moment(expireAt).format('YYYY-MM-DD, h:mm:ss a') : 'never'}
            </div>
        </div>
    )
}
