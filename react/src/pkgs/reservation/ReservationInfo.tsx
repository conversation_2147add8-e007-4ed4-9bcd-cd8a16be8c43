import { LoadingSpinner } from '@/common/components/CenteredSpinner'
import IconButton from '@mui/material/IconButton'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { LockOpen, SyncProblem } from '@mui/icons-material'
import LockIcon from '@mui/icons-material/Lock'
import { ReservableInfo } from '@/pkgs/reservation/useReservableInfo'
import { useState } from 'react'
import { LockingDialog } from '@/pkgs/reservation/LockingDialog'

type ReservationInfoProps = {
    reservationKey: string
    isActive: boolean | null
    reservableInfo: ReservableInfo
}

export function ReservationInfo({ reservationKey, isActive, reservableInfo }: ReservationInfoProps) {
    const { result, color, message, locked } = reservableInfo
    const [lockingDialogIsOpen, setLockingDialogIsOpen] = useState(false)

    if (result.isLoading) {
        return <LoadingSpinner />
    }

    if (result.isError) {
        return (
            <IconButton color={'error'} onClick={() => result.refetch()} title={guessErrorMessage(result.error)}>
                <SyncProblem />
            </IconButton>
        )
    }

    return (
        <>
            <IconButton color={color} onClick={() => setLockingDialogIsOpen(true)} title={message}>
                {locked ? <LockIcon /> : <LockOpen />}
            </IconButton>
            {lockingDialogIsOpen && isActive !== null && (
                <LockingDialog
                    open={lockingDialogIsOpen}
                    onClose={() => setLockingDialogIsOpen(false)}
                    reservationKey={reservationKey}
                    isActive={isActive}
                    reservableInfo={reservableInfo}
                />
            )}
        </>
    )
}
