import CMTextField, { CMTextFieldProps } from '@/common/components/CMTextField'
import { Chip, InputAdornment } from '@mui/material'

type FileExtensionTextFieldProps = {
    onChange: (value: string) => void
} & CMTextFieldProps

export function FileExtensionTextField({ ...cmTextFieldProps }: FileExtensionTextFieldProps) {
    const extension = (cmTextFieldProps?.value as string)?.split('.').pop()
    return (
        <CMTextField
            {...cmTextFieldProps}
            value={(cmTextFieldProps?.value as string)?.split('.').shift() || ''}
            onChange={(ev) => {
                const value = ev.target.value
                const valueWithExtension = `${value}.${extension}`
                cmTextFieldProps?.onChange?.(valueWithExtension)
            }}
            InputProps={{
                ...cmTextFieldProps.InputProps,
                endAdornment: (
                    <InputAdornment position={'end'}>
                        <Chip label={`.${extension}`} />
                    </InputAdornment>
                )
            }}
        />
    )
}
