import { httpDelete, httpGet, httpPost, httpPut } from '@/common/client'
import { ContentAPIV2, DocumentsAPI, ImagesAPI } from '@/common/constants'
import { z } from 'zod'
import { content } from '../content/types'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { guessErrorMessage, ZodErrorMsg } from '@/helpers/guessErrorMessage'
import { baseQueryConfig, dateTime, paged } from '@/common/react-query'
import { useImageCropSizeQuery } from '../system/image-crop-size/queries'
import { ImageCropSize } from '../system/image-crop-size/types'

export enum ResourceType {
    Image = 'image',
    Document = 'document',
    Folder = 'folder',
    Fragment = 'fragment'
}

const resourceRef = z.object({
    EntityID: z.string(),
    EntityType: z.string()
})

export type ResourceRef = z.infer<typeof resourceRef>

export function useResourceLocationQuery({ resources }: { resources: string[] }) {
    const queryKey = ['resource-location', resources]
    return useQuery({
        queryKey: queryKey,
        queryFn: async () => httpPost(`${ContentAPIV2}/resources`, { resources }, z.array(resourceRef).nullish()),
        enabled: !!resources.length
    })
}

// TODO: These types should be removed once storage manager is refactored
export const pgUUID = z.object({
    UUID: z.string().nullish(),
    Valid: z.boolean()
})

const image = z.object({
    id: z.string(),
    // active: z.boolean(),
    alt: z.string(),
    content_id: pgUUID,
    created: dateTime,
    deleted: dateTime,
    department_id: z.string().nullish(),
    filename: z.string(),
    // path: z.string().nullish(),
    tags: z.array(z.string()).nullish(),
    sites: z.array(z.string()).nullish(),
    type: z.string().nullish(),
    updated: dateTime,
    image_crop_size_ids: z.array(z.string()).nullish(),
    dirty_image_crop_size_ids: z.array(z.string()).nullish()
})

export const images = paged.extend({
    Rows: z.array(image)
})

export const imageDto = z.object({
    id: z.string(), // v4()
    type: z.string(), // 'image'
    department_id: z.string().nullish(),
    sites: z.array(z.string()).nullish(),
    filename: z
        .string()
        .regex(/^[a-zA-Z0-9-_ ]+\.[a-z]{3,4}$/i, 'Filename can only contain letters, numbers, dashes and underscores'),
    alt: z.string().min(3, 'Alt text must be at least 3 characters long'),
    tags: z.array(z.string()).nullish(),
    thumbnail: z.string().nullish(),
    data: z.string().nullish()
})

// as returned from api
export type Image = z.infer<typeof image>

// used for form/upserting
export type ImageDTO = z.infer<typeof imageDto>

export interface ImageSearchQueryParams {
    Search: string
}

export function useImageSearchQuery(params: ImageSearchQueryParams) {
    return useQuery({
        ...baseQueryConfig,
        queryKey: ['image-search', params],
        queryFn: async ({ signal }) => httpGet(ImagesAPI, params, images)
    })
}

export function useImageQuery(imageId: string) {
    const results = useImageSearchQuery({ Search: imageId })

    return { ...results, data: results?.data?.Rows?.[0] as Image }
}

export const upsertImageCropQuery = ({
    imageId,
    size,
    data
}: {
    imageId: string
    size: ImageCropSize
    data: string
}) => {
    if (!imageId) {
        console.warn(image)
        return Promise.reject('imageId is missing')
    }

    if (!data) {
        console.warn(image)
        return Promise.reject('base64 data for image is missing')
    }

    if (!size?.ID || !size.Name) {
        console.warn(image)
        return Promise.reject('crop size for image is missing')
    }

    return httpPut(`${ImagesAPI}/${imageId}/crop/${size.ID}`, { data }, z.any())
}

export const createImageQuery = (image: ImageDTO) => httpPost(ImagesAPI, image, z.any())

export const updateImageQuery = (image: ImageDTO) => httpPut(ImagesAPI, image, z.any())

export const deleteImageQuery = (imageId: string) => httpDelete(`${ImagesAPI}/${imageId}`)
