import { Field, isQueryableType, QueryableType, SelectOption } from '@/pkgs/queries/types'
import { Structure } from '@/pkgs/structure/types'
import { commonFields, contentFields, documentFields, eventFields } from './fieldsConfig'

export function getFields(
    type: 'content' | 'event' | 'document' | 'image',
    structure: Structure | undefined | null
): Field[] {
    const common = [...commonFields]
    if (type === 'content') {
        common.push(...contentFields)
    } else if (type === 'event') {
        common.push(...eventFields)
        common.push(...contentFields)
    } else if (type === 'image') {
        // TBD
    } else if (type === 'document') {
        common.push(...documentFields)
    }

    if (!structure) {
        return sortFields(common)
    }

    structure.FormStructure.filter((f) => !f.allowMultiple).forEach((form) => {
        const prefix = form.name
        const filters = form.components
            .filter((c) => isQueryableType(c.type))
            .map((c) => {
                const name = `${prefix}.${c.name}`
                return {
                    name,
                    label: `${c.label}`,
                    type: c.type as QueryableType,
                    options: c.options as SelectOption[]
                }
            })
        common.push(...filters)
    })

    structure.FormStructure.filter((f) => f.allowMultiple).forEach((form) => {
        common.push({
            name: form.name,
            type: 'array',
            label: form.title
        })
    })

    return sortFields(common)
}

export function sortFields(fields: Field[]): Field[] {
    return fields.sort((a, b) => a.label.localeCompare(b.label))
}
