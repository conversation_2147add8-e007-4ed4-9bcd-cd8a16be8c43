# E2E

To use/debug the E2E tests locally:
- you should have the environment variable CM_E2E=1. You can configure it in your IDE or in the terminal. 
    - before starting the `api` service ensure that you created/updated schema for e2e tenants (you can re-run `.dev/local_restore.sh`, or use `.dev/migrate_e2e.sh` for this)
    - if the variable is set `api` will seed the data into databases (you can check what is seeded here `go/tests/db_seed/seed.go`)
- E2E tests use `.localhost` domains. So check if they are set up correctly 
  (if you have `caddy` running, you should be fine, otherwise use any other tool to properly maintain the domains)
- There is no need to use `docker-compose.e2e.yml` locally. You can do that, but you should tear down the dev environment first.  
  With `docker-compose.e2e.yml` you won't be able to debug the services. 

## Essential commands

To run the E2E tests use `npx playwright test` in the `e2e` folder.

To run a project: `npx playwright test --project=lexical-shared --headed --debug`

To install and run the E2E tests:
```bash
cd e2e
npm ci          
npx playwright install --with-deps
npx playwright test
```
- debug, visual, slowMo: `SLOW_MO="500" npx playwright test --debug --headed` (Windows: `$env:SLOW_MO="500"; npx playwright test --debug --headed`)
- to start test recorder: `npx playwright codegen --viewport-size=1800,1000 contentmanager.imagineeverything.ca.localhost` 
- to log in to AWS ECR (if you want to pull a  container): `aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin 793554545599.dkr.ecr.ca-central-1.amazonaws.com`
- `docker-compose -p e2e -f ./e2e/docker-compose.e2e.yml up -d` / `docker-compose -p e2e -f ./e2e/docker-compose.e2e.yml down`
- to rebuild the migrator image: `docker-compose -p e2e -f ./e2e/docker-compose.e2e.yml build migrator`

## Seeded data

- Admin URL: `tenant-1.contentmanager.imagineeverything.ca.localhost`
- Tenant 1 `cm_tenant_1`:
    - Admin user: `<EMAIL> 12qwaszx`
    - Sites:
        - Site 1: `site-1.tenant-1.localhost`
        - Site 2: `site-2.tenant-1.localhost`
        - Site 3: `site-3.tenant-1.localhost`
        - Department: hosts are `site-1.tenant-1.localhost` and `site-2.tenant-1.localhost`
- Tenant 2 `cm_tenant_2`:
    - Admin user: `<EMAIL> 12qwaszx`
    - Sites:
        - Site 1: `site-1.tenant-2.localhost` 
